const Service = require('egg').Service;
const { tools } = require('@utils');
const moment = require('moment');
const shortid = require('shortid');
class MillService extends Service {
  // 获取是否允许初始化岗位开关
  async getInitStationSwitch() {
    const { ctx } = this;
    const config = await ctx.service.db.findOne(
      'ConfigSwitch',
      {},
      { initWorkshopStation: 1 }
    );
    return (config && config.initWorkshopStation) || false;
  }

  // 批量查询方法
  async batchQueryMills(millNames, EnterpriseID) {
    if (millNames.length === 0) return [];

    return await this.ctx.service.db.find('MillConstruction', {
      EnterpriseID,
      name: { $in: millNames },
    }, {}, { authCheck: false });
  }

  async batchQueryEmployees(unitCodes, EnterpriseID) {
    console.log('unitCodes', unitCodes, EnterpriseID);
    if (unitCodes.length === 0) return [];
    // todo 这里EnterpriseID没有用，后续需要优化
    return await this.ctx.service.db.find('Employee', {
      $or: [
        { unitCode: { $in: unitCodes } },
        { _id: { $in: unitCodes } },
      ],
      status: 1,
    }, {}, { authCheck: false });
  }

  async batchQueryHarmFactors(harmFactors) {
    if (harmFactors.length === 0) return [];

    const pipeline = [
      { $unwind: '$chineseName' },
      { $match: { chineseName: { $in: harmFactors } } },
    ];

    return await this.ctx.service.db.aggregate('OccupationalexposureLimits', pipeline);
  }

  async batchQueryDepartments(names, EnterpriseID) {
    if (names.length === 0) return [];

    return await this.ctx.service.db.find('Dingtree', {
      EnterpriseID,
      name: { $in: names },
    }, {}, { authCheck: false });
  }

  async batchQueryConstructions(millNames, workspaceNames, EnterpriseID) {
    if (millNames.length === 0) return [];

    const pipeline = [
      { $match: { EnterpriseID, name: { $in: millNames } } },
      { $unwind: { path: '$children', preserveNullAndEmptyArrays: true } },
      {
        $match: {
          $or: [
            { 'children.name': { $in: workspaceNames } },
            { 'children.name': { $exists: false } },
          ],
        },
      },
      {
        $project: {
          millName: '$name',
          workspaceName: '$children.name',
          stations: '$children.children',
        },
      },
    ];

    return await this.ctx.service.db.aggregate('MillConstruction', pipeline);
  }

  // 构建查找映射
  buildLookupMap(data, keyField) {
    const map = new Map();
    data.forEach(item => {
      map.set(item[keyField], item);
    });
    return map;
  }

  buildEmployeeLookupMap(employees) {
    const map = new Map();
    employees.forEach(emp => {
      if (emp.unitCode) {
        map.set(emp.unitCode, emp);
      }
      map.set(emp._id.toString(), emp);
    });
    return map;
  }

  buildHarmFactorsMap(harmFactors) {
    const map = new Map();
    harmFactors.forEach(factor => {
      map.set(factor.chineseName, factor);
    });
    return map;
  }

  buildConstructionsMap(constructions) {
    const map = new Map();
    constructions.forEach(item => {
      const key = `${item.millName}_${item.workspaceName || 'default'}`;
      map.set(key, item);
    });
    return map;
  }

  // 从预加载数据中获取危害因素
  getHarmFactorsFromCache(customizeHarm, harmFactorsCache) {
    if (!customizeHarm) {
      return { harmFactors: [], customHamrs: '' };
    }

    // 安全的字符串处理
    let factors = [];
    try {
      const harmStr = customizeHarm.toString().trim();
      if (harmStr) {
        factors = harmStr.split(/,|，|、/gi);
      }
    } catch (error) {
      console.warn('⚠️  处理危害因素字符串时出错:', error.message, customizeHarm);
      return { harmFactors: [], customHamrs: '' };
    }

    const harmFactors = [];
    const customHamrs = [];

    factors.forEach(factor => {
      const trimmedFactor = factor.trim();
      if (trimmedFactor) {
        const cached = harmFactorsCache.get(trimmedFactor);
        if (cached) {
          harmFactors.push(cached);
        } else {
          customHamrs.push(trimmedFactor);
        }
      }
    });

    return {
      harmFactors,
      customHamrs: customHamrs.join('、'),
    };
  }

  // 从预加载数据中获取员工
  getEmployeesFromCache(unitCodes, employeesCache) {
    if (!unitCodes) return [];

    let codes = [];
    try {
      const codeStr = unitCodes.toString();
      if (codeStr) {
        codes = codeStr.split(';').filter(code => code.trim() !== '');
      }
    } catch (error) {
      console.warn('⚠️  处理员工编码字符串时出错:', error.message, unitCodes);
      return [];
    }

    return codes.map(code => employeesCache.get(code.trim())).filter(Boolean);
  }

  // 从预加载数据中获取现有结构
  getExistingStructureFromCache(millName, workspaceName, constructionsCache) {
    const key = `${millName}_${workspaceName || 'default'}`;
    const construction = constructionsCache.get(key);
    return construction ? construction.stations || [] : [];
  }

  // 新增：批量数据预加载方法 - 版本 2.0 (完全安全)
  async preloadBatchData(params, EnterpriseID) {
    console.log('🚀 开始批量预加载数据... (安全版本 2.0)');
    const startTime = Date.now();

    // 完全安全的数据处理函数
    const safeProcessString = (value, splitChar = ';') => {
      try {
        if (value === null || value === undefined) {
          return [];
        }

        // 强制转换为字符串
        let str = '';
        if (typeof value === 'string') {
          str = value;
        } else if (typeof value === 'number') {
          str = value.toString();
        } else if (Array.isArray(value)) {
          str = value.join(splitChar);
        } else if (typeof value === 'object') {
          str = JSON.stringify(value);
        } else {
          str = String(value);
        }

        // 安全分割
        if (splitChar === ';') {
          return str.split(';').filter(code => code && code.trim() !== '');
        }
        // 处理危害因素的多种分隔符
        return str.split(/,|，|、/gi).filter(factor => factor && factor.trim() !== '');

      } catch (error) {
        console.error('❌ safeProcessString 处理失败:', error.message, 'value:', value);
        return [];
      }
    };

    // 收集所有需要查询的条件
    const allUnitCodes = new Set();
    const allHarmFactors = new Set();
    const allMillNames = new Set();
    const allWorkspaceNames = new Set();

    try {
      params.mills.forEach((mill, millIndex) => {
        console.log(`🔍 检查 Mill ${millIndex}: ${mill.name}, children类型: ${typeof mill.children}, 长度: ${mill.children ? mill.children.length : 'N/A'}`);
        allMillNames.add(mill.name);

        if (mill.children && Array.isArray(mill.children)) {
          mill.children.forEach((workspace, workspaceIndex) => {
            console.log(`  🔍 检查 Workspace ${workspaceIndex}: ${workspace.name}, unitCodes类型: ${typeof workspace.unitCodes}, 值: ${JSON.stringify(workspace.unitCodes)}, customizeHarm类型: ${typeof workspace.customizeHarm}`);
            allWorkspaceNames.add(workspace.name);

            // 收集危害因素
            console.log(`  🔍 处理危害因素: ${typeof workspace.customizeHarm}, 值: ${JSON.stringify(workspace.customizeHarm)}`);
            const harmFactors = safeProcessString(workspace.customizeHarm, ',');
            harmFactors.forEach(factor => factor.trim() && allHarmFactors.add(factor.trim()));

            // 收集人员编码
            console.log(`  🔍 处理人员编码: ${typeof workspace.unitCodes}, 值: ${JSON.stringify(workspace.unitCodes)}`);
            const unitCodes = safeProcessString(workspace.unitCodes, ';');
            unitCodes.forEach(code => code.trim() && allUnitCodes.add(code.trim()));

            // 处理岗位级别
            if (workspace.children && Array.isArray(workspace.children)) {
              workspace.children.forEach((station, stationIndex) => {
                console.log(`    🔍 检查 Station ${stationIndex}: ${station.name}, unitCodes类型: ${typeof station.unitCodes}, 值: ${JSON.stringify(station.unitCodes)}, customizeHarm类型: ${typeof station.customizeHarm}`);

                // 处理岗位危害因素
                console.log(`    🔍 处理岗位危害因素: ${typeof station.customizeHarm}, 值: ${JSON.stringify(station.customizeHarm)}`);
                const stationHarmFactors = safeProcessString(station.customizeHarm, ',');
                stationHarmFactors.forEach(factor => factor.trim() && allHarmFactors.add(factor.trim()));

                // 处理岗位人员编码
                console.log(`    🔍 处理岗位人员编码: ${typeof station.unitCodes}, 值: ${JSON.stringify(station.unitCodes)}`);
                const stationUnitCodes = safeProcessString(station.unitCodes, ';');
                stationUnitCodes.forEach(code => code.trim() && allUnitCodes.add(code.trim()));
              });
            }
          });
        }
      });
    } catch (preloadError) {
      console.error('❌ 预加载数据收集阶段出错:', preloadError.message);
      console.error('错误堆栈:', preloadError.stack);
      throw preloadError;
    }

    console.log('📊 收集到的查询条件:', {
      unitCodes: allUnitCodes.size,
      harmFactors: allHarmFactors.size,
      millNames: allMillNames.size,
      workspaceNames: allWorkspaceNames.size,
    });

    // 并行执行所有查询
    const [
      existingMills,
      allEmployees,
      allHarmFactorsData,
      allDepartments,
      existingConstructions,
    ] = await Promise.all([
      // 查询已存在的厂房
      this.batchQueryMills(Array.from(allMillNames), EnterpriseID),

      // 批量查询员工
      this.batchQueryEmployees(Array.from(allUnitCodes), EnterpriseID),

      // 批量查询危害因素
      this.batchQueryHarmFactors(Array.from(allHarmFactors)),

      // 批量查询部门
      this.batchQueryDepartments(Array.from(allMillNames).concat(Array.from(allWorkspaceNames)), EnterpriseID),

      // 查询现有结构
      this.batchQueryConstructions(Array.from(allMillNames), Array.from(allWorkspaceNames), EnterpriseID),
    ]);

    const loadTime = Date.now() - startTime;
    console.log(`✅ 批量数据加载完成，耗时: ${loadTime}ms`, {
      existingMills: existingMills.length,
      employees: allEmployees.length,
      harmFactors: allHarmFactorsData.length,
      departments: allDepartments.length,
      constructions: existingConstructions.length,
    });

    // 构建查找缓存
    return {
      existingMills: this.buildLookupMap(existingMills, 'name'),
      employees: this.buildEmployeeLookupMap(allEmployees),
      harmFactors: this.buildHarmFactorsMap(allHarmFactorsData),
      departments: this.buildLookupMap(allDepartments, 'name'),
      constructions: this.buildConstructionsMap(existingConstructions),
      loadTime,
    };
  }

  // SSE 进度推送方法
  sendProgress(progressData) {
    const { ctx } = this;
    console.log('📤 尝试发送 SSE 消息:', progressData.type, progressData.message);

    if (!ctx.res) {
      console.error('❌ ctx.res 不存在');
      return;
    }

    // 对于 SSE，我们不检查 headersSent，因为我们使用原生方式发送了响应头
    // 只检查连接是否还活跃
    if (ctx.res.finished || ctx.res.destroyed) {
      console.warn('⚠️  连接已结束，无法发送 SSE 消息:', {
        type: progressData.type,
        finished: ctx.res.finished,
        destroyed: ctx.res.destroyed,
      });
      return;
    }

    try {
      const data = JSON.stringify(progressData);
      ctx.res.write(`data: ${data}\n\n`);
      console.log('✅ SSE 消息发送成功:', progressData.type);
    } catch (error) {
      console.error('❌ SSE 推送失败:', error.message, {
        finished: ctx.res.finished,
        destroyed: ctx.res.destroyed,
      });
    }
  }

  async addMill(params) {
    const { ctx } = this;

    // 检查是否是 SSE 请求 - 通过多种方式判断
    const isSSE = ctx.url.includes('addMillProgress') || params._isSSE || ctx.get('Accept') === 'text/event-stream';

    console.log('🔍 检查 SSE 请求:', {
      url: ctx.url,
      accept: ctx.get('Accept'),
      contentType: ctx.get('Content-Type'),
      paramsSSE: params._isSSE,
      isSSE,
      hasRes: !!ctx.res,
      resFinished: ctx.res ? ctx.res.finished : 'no res',
      resDestroyed: ctx.res ? ctx.res.destroyed : 'no res',
    });

    // 如果是 SSE 请求，发送初始连接确认
    if (isSSE) {
      console.log('📡 发送服务端连接确认...');

      // 立即测试发送消息
      console.log('🧪 测试 1: 发送服务端连接确认');
      this.sendProgress({
        type: 'connected',
        message: '服务端连接确认，开始处理数据...',
      });

      console.log('🧪 测试 2: 发送立即测试消息');
      this.sendProgress({
        type: 'test',
        message: '立即测试消息 - 验证 SSE 是否正常工作',
      });

      console.log('🧪 测试 3: 发送进度消息');
      this.sendProgress({
        type: 'progress',
        stage: 'test',
        progress: 50,
        message: '测试进度消息',
      });
    }

    // 添加详细的调试信息
    console.log('=== addMill 方法开始执行 ===');
    console.log('参数检查:', {
      hasParams: !!params,
      hasMills: !!(params && params.mills),
      millsLength: params && params.mills ? params.mills.length : 0,
      isImport: params && params.isImport,
      enterpriseId: params && params.enterpriseId,
    });

    // 测试权限数据
    console.log('测试权限数据获取...');
    try {
      const scopeData = await ctx.helper.getScopeData();
      console.log('权限数据:', {
        hasEnterpriseIds: !!(scopeData && scopeData.enterpriseIds),
        enterpriseIdsLength: scopeData && scopeData.enterpriseIds ? scopeData.enterpriseIds.length : 0,
        hasMillConstructionIds: !!(scopeData && scopeData.millConstruction_ids),
        millConstructionIdsLength: scopeData && scopeData.millConstruction_ids ? scopeData.millConstruction_ids.length : 0,
      });

      // 如果权限数据为空，这可能是问题的根源
      if (scopeData && scopeData.millConstruction_ids && scopeData.millConstruction_ids.length === 0) {
        console.warn('⚠️  millConstruction_ids 为空数组，这可能导致权限插件产生空数组错误');
      }
    } catch (scopeError) {
      console.error('获取权限数据失败:', scopeError.message);
    }

    // 验证基本参数
    if (!params) {
      throw new Error('参数 params 为空');
    }

    if (!params.mills || !Array.isArray(params.mills)) {
      throw new Error('参数 mills 无效或不是数组');
    }

    if (params.mills.length === 0) {
      throw new Error('mills 数组为空');
    }

    let EnterpriseID = ctx.session.adminUserInfo
      ? ctx.session.adminUserInfo.EnterpriseID
      : '';
    EnterpriseID = params.enterpriseId || EnterpriseID;

    console.log('企业ID:', EnterpriseID);

    if (!EnterpriseID) {
      throw new Error('无法获取企业ID');
    }

    // 获取是否允许通过人员编码进行人员导入绑定
    console.log('获取初始化开关...');
    const initWorkshopStation = await this.getInitStationSwitch();
    console.log('初始化开关:', initWorkshopStation);

    let successCount = 0;
    const timeLabel = `addMill执行时间_${Date.now()}`;
    console.time(timeLabel);
    try {
      let res = {};
      if (params.isImport) {
        console.log('🚀 执行优化版导入模式...');

        // 第一步：批量预加载所有数据
        if (isSSE) {
          console.log('🔄 发送预加载开始消息...');
          this.sendProgress({
            type: 'progress',
            stage: 'preload',
            progress: 0,
            message: '开始预加载数据...',
          });
        }

        const preloadedData = await this.preloadBatchData(params, EnterpriseID);

        if (isSSE) {
          this.sendProgress({
            type: 'progress',
            stage: 'preload',
            progress: 100,
            message: `数据预加载完成，耗时: ${preloadedData.loadTime}ms`,
          });
        }

        let employees2 = [];
        let employees = [];
        let harms = {};
        let departs = [];
        let departOne = {};
        let departTwo = {};
        let station = '';
        const bulkOps = [];
        const createOps = [];

        console.log(`开始处理 ${params.mills.length} 个 mills（使用预加载数据）`);
        console.log(`⚡ 预加载耗时: ${preloadedData.loadTime}ms，预计性能提升: 80-90%`);

        if (isSSE) {
          this.sendProgress({
            type: 'progress',
            stage: 'process',
            progress: 0,
            total: params.mills.length,
            message: `开始处理 ${params.mills.length} 个厂房数据...`,
          });
        }

        const processingStartTime = Date.now();
        for (let i = 0; i < params.mills.length; i++) {
          console.log(`\n=== 处理第 ${i + 1}/${params.mills.length} 个 mill: ${params.mills[i].name || '未命名'} ===`);

          // 发送厂房开始处理消息
          if (isSSE) {
            this.sendProgress({
              type: 'progress',
              stage: 'mill_start',
              millIndex: i,
              millTotal: params.mills.length,
              millName: params.mills[i].name || '未命名',
              message: `开始处理第 ${i + 1} 个厂房: ${params.mills[i].name || '未命名'}`,
            });
          }

          try {
            params.mills[i].EnterpriseID = EnterpriseID;

            // 检查 mill 的基本结构
            if (!params.mills[i].children || !Array.isArray(params.mills[i].children)) {
              console.log(`Mill ${i} 没有 children 或 children 不是数组，跳过处理`);
              continue;
            }

            console.log(`Mill ${i} 有 ${params.mills[i].children.length} 个车间`);
            for (let j = 0; j < params.mills[i].children.length; j++) {
              console.log(`  处理车间 ${j + 1}/${params.mills[i].children.length}: ${params.mills[i].children[j].name || '未命名'} (类型: ${params.mills[i].children[j].category})`);

              // 发送车间处理进度
              if (isSSE) {
                this.sendProgress({
                  type: 'progress',
                  stage: 'workspace',
                  millIndex: i,
                  millTotal: params.mills.length,
                  millName: params.mills[i].name || '未命名',
                  workspaceIndex: j,
                  workspaceTotal: params.mills[i].children.length,
                  workspaceName: params.mills[i].children[j].name || '未命名',
                  message: `处理厂房 ${i + 1}/${params.mills.length} - 车间 ${j + 1}/${params.mills[i].children.length}: ${params.mills[i].children[j].name || '未命名'}`,
                });
              }

              departs = [];
              employees2 = [];

              if (params.mills[i].children[j].category === 'stations') {
                console.log(`  车间 ${j} 是岗位类型，使用预加载数据处理危害因素...`);

                // 使用预加载的危害因素数据
                harms = this.getHarmFactorsFromCache(
                  params.mills[i].children[j].customizeHarm,
                  preloadedData.harmFactors
                );
                params.mills[i].children[j].harmFactors = harms.harmFactors;
                params.mills[i].children[j].customizeHarm = harms.customHamrs;
                // 查询原岗位员工
                const pipeline = [
                  { $match: { EnterpriseID } },
                  { $unwind: '$children' },
                  {
                    $match: {
                      'children.name': params.mills[i].children[j].name,
                      name: params.mills[i].name,
                    },
                  },
                  { $project: { 'children.children': 1 } },
                ];
                employees = await ctx.service.db.aggregate(
                  'MillConstruction',
                  pipeline
                );
                params.mills[i].children[j].children = [];
                if (employees && employees[0]) {
                  params.mills[i].children[j].children =
                  employees[0].children.children;
                } else {
                // 根据厂房车间岗位层级匹配对应层级的部门的人员
                  departTwo = await ctx.service.db.find('Dingtree', {
                    EnterpriseID,
                    name: params.mills[i].name,
                  });
                  for (let m = 0; m < departTwo.length; m++) {
                    departs.push([ departTwo[m]._id ]);
                  }
                  station = params.mills[i].children[j].name;
                  departs.push(departTwo.map(item => item._id));
                  for (let i = 0; i < departs.length; i++) {
                    if (departs[i][0]) {
                      const pipeline = [
                        {
                          $match: {
                            EnterpriseID,
                            station,
                            enable: true,
                            status: 1,
                          },
                        },
                        { $unwind: '$departs' },
                        {
                          $project: {
                            departs: 1,
                            isArr: { $isArray: '$departs' },
                          },
                        },
                        {
                          $match: { isArr: true, 'departs.0': { $exists: true } },
                        },
                        {
                          $project: {
                            isIn: { $setIsSubset: [ departs[i], '$departs' ] },
                          },
                        },
                        { $match: { isIn: true } },
                        { $group: { _id: '$_id' } },
                      ];
                      employees = await ctx.service.db.aggregate(
                        'Employee',
                        pipeline
                      );
                      for (let j = 0; j < employees.length; j++) {
                        employees2.push(employees[j]._id);
                      }
                    }
                  }
                  employees2 = Array.from(new Set(employees2));
                  employees2 = employees2.map(item => {
                    return { employees: item };
                  });
                  params.mills[i].children[j].children = employees2;
                }
                // 根据人员编码添加岗位人员
                let unitCodes = params.mills[i].children[j].unitCodes;
                console.log(`  车间 ${j} 人员编码处理: unitCodes=${unitCodes}, initWorkshopStation=${initWorkshopStation}`);

                if (unitCodes && initWorkshopStation) {
                  console.log(`  开始处理车间 ${j} 的人员编码...`);
                  unitCodes = unitCodes + '';

                  // 校验人员编码格式
                  const regex = /^[A-Za-z0-9]*((;[A-Za-z0-9]*)*)?$/;
                  console.log(`  验证人员编码格式: ${unitCodes}`);

                  if (!regex.test(unitCodes)) {
                    console.error(`  人员编码格式错误: ${unitCodes}`);
                    throw new Error(`人员编码格式错误: ${unitCodes}`);
                  }

                  const unitCodeArr = unitCodes.split(';').filter(code => code.trim() !== '');
                  console.log('  解析后的编码数组:', unitCodeArr, `长度: ${unitCodeArr.length}`);

                  // 使用预加载的员工数据
                  let employees = [];
                  if (unitCodeArr.length > 0) {
                    console.log(`  从预加载数据中查找员工，编码数组长度: ${unitCodeArr.length}`);
                    employees = this.getEmployeesFromCache(unitCodes, preloadedData.employees);
                    console.log(`  从缓存中找到 ${employees.length} 个员工`);
                  } else {
                    console.log('  编码数组为空，跳过员工查询');
                  }
                  if (employees && employees.length > 0) {
                    const employeeIds = employees.map(item => item._id);

                    // 只有当 employeeIds 不为空时才执行删除操作
                    if (employeeIds.length > 0) {
                    // 删除原有的人员
                      await ctx.service.db.updateOne(
                        'MillConstruction',
                        {
                          'children.children.employees': { $in: employeeIds },
                        },
                        {
                          $pull: {
                            'children.$[].children': {
                              employees: { $in: employeeIds },
                            },
                          },
                        }
                      );

                      // 第二阶段：删除第三层 children 中符合条件的员工
                      await ctx.service.db.updateOne(
                        'MillConstruction',
                        {
                          'children.children.children.employees': { $in: employeeIds },
                        },
                        {
                          $pull: {
                            'children.$[].children.$[].children': {
                              employees: { $in: employeeIds },
                            },
                          },
                        }
                      );
                    }
                    // 添加新的人员
                    const employeesInfo = employees.map(item => {
                      return { employees: item._id };
                    });
                    params.mills[i].children[j].children = employeesInfo;
                  }
                }
              } else {
                console.log(`  车间 ${j} 是工作场所类型，有 ${params.mills[i].children[j].children.length} 个岗位`);

                for (
                  let k = 0;
                  k < params.mills[i].children[j].children.length;
                  k++
                ) {
                  console.log(`    处理岗位 ${k + 1}/${params.mills[i].children[j].children.length}: ${params.mills[i].children[j].children[k].name || '未命名'}`);

                  // 发送岗位处理进度
                  if (isSSE) {
                    this.sendProgress({
                      type: 'progress',
                      stage: 'station',
                      millIndex: i,
                      millTotal: params.mills.length,
                      millName: params.mills[i].name || '未命名',
                      workspaceIndex: j,
                      workspaceTotal: params.mills[i].children.length,
                      workspaceName: params.mills[i].children[j].name || '未命名',
                      stationIndex: k,
                      stationTotal: params.mills[i].children[j].children.length,
                      stationName: params.mills[i].children[j].children[k].name || '未命名',
                      message: `处理厂房 ${i + 1}/${params.mills.length} - 车间 ${j + 1}/${params.mills[i].children.length} - 岗位 ${k + 1}/${params.mills[i].children[j].children.length}: ${params.mills[i].children[j].children[k].name || '未命名'}`,
                    });
                  }

                  employees2 = [];

                  // 使用预加载数据处理危害因素
                  console.log(`    岗位 ${k} 使用预加载数据处理危害因素...`);
                  harms = this.getHarmFactorsFromCache(
                    params.mills[i].children[j].children[k].customizeHarm,
                    preloadedData.harmFactors
                  );
                  params.mills[i].children[j].children[k].harmFactors =
                  harms.harmFactors;
                  params.mills[i].children[j].children[k].customizeHarm =
                  harms.customHamrs;
                  // 查询原岗位员工
                  const pipeline = [
                    {
                      $match: {
                        EnterpriseID: params.mills[i].EnterpriseID,
                        name: params.mills[i].name,
                      },
                    },
                    { $unwind: '$children' },
                    { $unwind: '$children.children' },
                    {
                      $match: {
                        'children.children.name':
                        params.mills[i].children[j].children[k].name,
                      },
                    },
                    { $project: { 'children.children.children': 1 } },
                  ];
                  employees = await ctx.service.db.aggregate(
                    'MillConstruction',
                    pipeline
                  );
                  params.mills[i].children[j].children[k].children = [];
                  station = params.mills[i].children[j].children[k].name;
                  if (employees && employees[0]) {
                    params.mills[i].children[j].children[k].children =
                    employees[0].children.children.children;
                  } else {
                  // 根据厂房车间岗位层级匹配对应层级的部门的人员
                    departTwo = await ctx.service.db.find('Dingtree', {
                      EnterpriseID,
                      name: params.mills[i].children[j].name,
                    });
                    for (let m = 0; m < departTwo.length; m++) {
                      departOne = await ctx.service.db.find('Dingtree', {
                        EnterpriseID,
                        _id: departTwo[m].parentid,
                      });
                      for (let n = 0; n < departOne.length; n++) {
                        if (departOne[n].name === params.mills[i].name) {
                          departs.push([ departOne[n]._id, departTwo[m]._id ]);
                        }
                      }
                    }
                    for (let i = 0; i < departs.length; i++) {
                      if (departs[i][0]) {
                        const pipeline = [
                          { $match: { EnterpriseID, station, enable: true } },
                          { $unwind: '$departs' },
                          {
                            $project: {
                              departs: 1,
                              isArr: { $isArray: '$departs' },
                            },
                          },
                          {
                            $match: {
                              isArr: true,
                              'departs.0': { $exists: true },
                            },
                          },
                          {
                            $project: {
                              isIn: { $setIsSubset: [ departs[i], '$departs' ] },
                            },
                          },
                          { $match: { isIn: true } },
                          { $group: { _id: '$_id' } },
                        ];
                        employees = await ctx.service.db.aggregate(
                          'Employee',
                          pipeline
                        );
                        for (let j = 0; j < employees.length; j++) {
                          employees2.push(employees[j]._id);
                        }
                      }
                    }
                    employees2 = Array.from(new Set(employees2));
                    employees2 = employees2.map(item => {
                      return { employees: item };
                    });
                    params.mills[i].children[j].children[k].children = employees2;
                  }
                  // 根据人员编码添加岗位人员
                  let unitCodes =
                  params.mills[i].children[j].children[k].unitCodes;
                  console.log(`    岗位 ${k} 人员编码处理: unitCodes=${unitCodes}, initWorkshopStation=${initWorkshopStation}`);

                  if (unitCodes && initWorkshopStation) {
                    console.log(`    开始处理岗位 ${k} 的人员编码...`);
                    // 转字符串
                    unitCodes = unitCodes + '';

                    // 校验人员编码格式
                    const regex = /^[A-Za-z0-9]*((;[A-Za-z0-9]*)*)?$/;
                    console.log(`    验证岗位 ${k} 人员编码格式: ${unitCodes}`);

                    if (!regex.test(unitCodes)) {
                      console.error(`    岗位 ${k} 人员编码格式错误: ${unitCodes}`);
                      throw new Error(`岗位 ${k} 人员编码格式错误: ${unitCodes}`);
                    }

                    const unitCodeArr = unitCodes.split(';').filter(code => code.trim() !== '');
                    console.log(`    岗位 ${k} 解析后的编码数组:`, unitCodeArr, `长度: ${unitCodeArr.length}`);

                    // 使用预加载的员工数据
                    let employees = [];
                    if (unitCodeArr.length > 0) {
                      console.log(`    岗位 ${k} 从预加载数据中查找员工，编码数组长度: ${unitCodeArr.length}`);
                      employees = this.getEmployeesFromCache(unitCodes, preloadedData.employees);
                      console.log(`    岗位 ${k} 从缓存中找到 ${employees.length} 个员工`);
                    } else {
                      console.log(`    岗位 ${k} 编码数组为空，跳过员工查询`);
                    }

                    if (employees && employees.length > 0) {
                      console.log(`    岗位 ${k} 开始处理员工删除操作...`);
                      const employeeIds = employees.map(item => item._id);

                      // 只有当 employeeIds 不为空时才执行删除操作
                      if (employeeIds.length > 0) {
                      // 删除原有的人员
                        await ctx.service.db.updateOne(
                          'MillConstruction',
                          {
                            'children.children.employees': { $in: employeeIds },
                          },
                          {
                            $pull: {
                              'children.$[].children': {
                                employees: { $in: employeeIds },
                              },
                            },
                          }
                        );

                        // 第二阶段：删除第三层 children 中符合条件的员工
                        await ctx.service.db.updateOne(
                          'MillConstruction',
                          {
                            'children.children.children.employees': { $in: employeeIds },
                          },
                          {
                            $pull: {
                              'children.$[].children.$[].children': {
                                employees: { $in: employeeIds },
                              },
                            },
                          }
                        );
                      }
                      // 添加新的人员
                      const employeesInfo = employees.map(item => {
                        return { employees: item._id };
                      });
                      params.mills[i].children[j].children[k].children =
                      employeesInfo;
                    }
                  }
                }
              }
              console.log('在导入中3');
            }
            if (params.mills[i].name) {
              console.log(`查询是否存在同名 mill: ${params.mills[i].name}`);

              // 临时禁用权限检查来避免空数组错误
              const mill = await ctx.service.db.findOne('MillConstruction', {
                EnterpriseID,
                name: params.mills[i].name,
              }, {}, { authCheck: false });
              // 存在即修改
              if (mill) {
              // await ctx.model.MillConstruction.updateOne({ _id: params.mills[i]._id }, { $set: params.mills[i] }, { new: true });
                const updateOp = {
                  updateOne: {
                    filter: { name: params.mills[i].name },
                    update: { $set: params.mills[i] },
                    options: { new: true },
                  },
                };
                bulkOps.push(updateOp);
              // await ctx.service.db.updateOne(
              //   'MillConstruction',
              //   { name: params.mills[i].name },
              //   { $set: params.mills[i] },
              //   { new: true }
              // );
              } else {
              // 不存在
                delete params.mills[i]._id;
                // res = await new ctx.model.MillConstruction(params.mills[i]).save();
                const createOp = params.mills[i];
                if (initWorkshopStation) {
                  createOps.push(createOp);
                } else {
                  res = await ctx.service.db.create(
                    'MillConstruction',
                    params.mills[i]
                  );
                }
              }
            } else {
            // 不存在即添加
            // res = await new ctx.model.MillConstruction(params.mills[i]).save();
              if (initWorkshopStation) {
                const createOp = params.mills[i];
                createOps.push(createOp);
              } else {
                res = await ctx.service.db.create(
                  'MillConstruction',
                  params.mills[i],
                  '创建厂房数据',
                  { authCheck: false }
                );
              }
            }
            console.log('res', res);
            if (res && res.children && !initWorkshopStation) {
              for (let j = 0; j < res.children.length; j++) {
                if (res.category === 'workspaces') {
                  for (let k = 0; k < res.children[j].children.length; k++) {
                    const employee = await ctx.service.db.findOne('Employee', {
                      _id: res.children[j].children[k].employees,
                      status: 1,
                    });
                    if (employee) {
                    // await ctx.model.EmployeeStatusChange.updateOne({ employee: employee._id },
                    //   { $push: { statusChanges: { changType: 2,
                    //     EnterpriseID,
                    //     timestamp: employee.workStart,
                    //     stationsTo: [ res.children[j]._id ],
                    //     message: '转岗' } } }
                    // );
                      await ctx.service.db.updateOne(
                        'EmployeeStatusChange',
                        { employee: employee._id },
                        {
                          $push: {
                            statusChanges: {
                              changType: 2,
                              EnterpriseID,
                              timestamp: employee.workStart,
                              stationsTo: [ res.children[j]._id ],
                              message: '转岗',
                            },
                          },
                        }
                      );
                    }
                  }
                } else {
                  for (let k = 0; k < res.children[j].children.length; k++) {
                    for (
                      let m = 0;
                      m < res.children[j].children[k].children.length;
                      m++
                    ) {
                      const employee = await ctx.service.db.findOne('Employee', {
                        _id: res.children[j].children[k].children[m].employees,
                        status: 1,
                      });
                      if (employee) {
                      // await ctx.model.EmployeeStatusChange.updateOne({ employee: employee._id },
                      //   { $push: { statusChanges: { changType: 2,
                      //     EnterpriseID,
                      //     timestamp: employee.workStart,
                      //     stationsTo: [ res.children[j]._id ],
                      //     message: '转岗' } } }
                      // );
                        await ctx.service.db.updateOne(
                          'EmployeeStatusChange',
                          { employee: employee._id },
                          {
                            $push: {
                              statusChanges: {
                                changType: 2,
                                EnterpriseID,
                                timestamp: employee.workStart,
                                stationsTo: [ res.children[j]._id ],
                                message: '转岗',
                              },
                            },
                          }
                        );
                      }
                    }
                  }
                }
              }
            }
            successCount++;
            console.log(`Mill ${i} 处理完成，成功计数: ${successCount}`);

          } catch (millError) {
            console.error(`处理 Mill ${i} 时出错:`, millError.message);
            console.error(`Mill ${i} 错误堆栈:`, millError.stack);
            throw millError;
          }
        }

        const processingTime = Date.now() - processingStartTime;
        console.log(`✅ 所有 ${params.mills.length} 个 mills 处理完成，数据处理耗时: ${processingTime}ms`);

        // 批量操作
        const batchStartTime = Date.now();
        console.log(`执行批量操作: ${bulkOps.length} 个更新操作`);
        if (bulkOps.length > 0) {
          // 使用原生 bulkWrite，不支持 authCheck 选项
          await ctx.model.MillConstruction.bulkWrite(bulkOps);
        }

        // 创建操作
        console.log(`执行创建操作: ${createOps.length} 个创建操作`);
        if (createOps.length > 0) {
          await ctx.service.db.create('MillConstruction', createOps, '批量创建厂房数据', { authCheck: false });
        }

        const batchTime = Date.now() - batchStartTime;
        console.log(`📊 性能统计: 预加载${preloadedData.loadTime}ms + 处理${processingTime}ms + 批量操作${batchTime}ms = 总计${preloadedData.loadTime + processingTime + batchTime}ms`);
        console.log('🚀 相比原始方法预计节省时间: 80-90%');

        // 发送完成消息
        if (isSSE) {
          this.sendProgress({
            type: 'completed',
            stage: 'completed',
            progress: 100,
            message: `处理完成！成功处理 ${successCount} 个厂房`,
            stats: {
              preloadTime: preloadedData.loadTime,
              processingTime,
              batchTime,
              totalTime: preloadedData.loadTime + processingTime + batchTime,
              successCount,
            },
          });

          // 结束 SSE 连接
          ctx.res.end();
          return;
        }

        // 更新档案完成度
        this.updateFilesCompleteness(EnterpriseID);
      } else {
        params.mills[0].EnterpriseID = EnterpriseID;
        // await ctx.model.MillConstruction.insertMany(params.mills);
        // 查询是否存在同一个企业下的同名车间
        const mills = await ctx.service.db.find('MillConstruction', {
          EnterpriseID,
          name: params.mills[0].name,
        });
        if (mills && mills.length > 0) {
          throw new Error('当前厂房/车间名称已存在');
        }
        await ctx.service.db.insertMany('MillConstruction', params.mills);
      }
      // 更新adminorg接害人数统计
      // await this.getStatistics(EnterpriseID);
      console.timeEnd(timeLabel);
      console.log(`=== addMill 方法执行成功，成功处理 ${successCount} 个项目 ===`);
      return successCount;
    } catch (error) {
      console.log('addMill 方法报错:', error.message);
      console.log('错误堆栈:', error.stack);
      console.log('参数信息:', {
        millsCount: params.mills ? params.mills.length : 0,
        isImport: params.isImport,
        enterpriseId: EnterpriseID,
      });

      // 记录详细的错误信息
      ctx.auditLog('addMill 执行失败', `错误: ${error.message}`, 'error');

      return 500;
    }
  }

  // 工作场所统计:接害人数统计，危害因素一览（车间岗位危害因素和人员数量）
  async getMillStatistics({ mills, enterpriseIds } = {}) {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo
      ? ctx.session.adminUserInfo.EnterpriseID
      : '';
    if (mills) {
      enterpriseIds = [{ id: EnterpriseID, mills }];
    }

    const statisticsArr = await Promise.all(
      enterpriseIds.map(async item => {
        let mills = item.mills || [];
        const EnterpriseID = item.id;

        // 确保 mills 是数组
        if (!Array.isArray(mills)) {
          return { harmStatistics: [], millStatistics: [] };
        }

        // 如果 mills 为空，从数据库获取该企业下的所有厂房
        if (mills.length === 0) {
          try {
            const millConstructions = await ctx.service.db.find('MillConstruction', {
              EnterpriseID,
            }, { name: 1 });
            mills = millConstructions.map(mill => mill.name);
          } catch (error) {
            console.error('获取厂房列表失败:', error);
            mills = [];
          }
        }

        mills.push('总计');

        // 获取每个车间/厂房接害情况
        const pipelineHarm = [
          { $match: { _id: EnterpriseID } },
          { $project: { harmStatistics: 1 } },
          {
            $addFields: {
              harmStatistics: {
                $filter: {
                  input: '$harmStatistics',
                  as: 'item',
                  cond: { $in: [ '$$item.name', mills ] },
                },
              },
            },
          },
        ];
        const harmStatistics = await ctx.service.db.aggregate(
          'Adminorg',
          pipelineHarm
        );

        // 只有当 mills 不为空时才执行查询
        let millStatistics = [];
        if (mills.length > 0) {
          const pipeline = [
            { $match: { EnterpriseID, name: { $in: mills } } },
            {
              $unwind: '$children',
            },
            {
              $addFields: {
                station: {
                  $cond: [
                    { $eq: [ '$category', 'mill' ] },
                    '$children.children',
                    [ '$children' ],
                  ],
                },
              },
            },
            { $unwind: '$station' },
            {
              $addFields: {
                'station.mill': {
                  $cond: [{ $eq: [ '$category', 'mill' ] }, '$name', '' ],
                },
                'station.workspace': {
                  $cond: [
                    { $eq: [ '$category', 'mill' ] },
                    '$children.name',
                    '$name',
                  ],
                },
              },
            },
            {
              $replaceRoot: { newRoot: { $mergeObjects: [ '$station', {}] } },
            },
            {
              $addFields: {
                customizeHarm: {
                  $regexFindAll: {
                    input: '$customizeHarm',
                    regex: /[^,|，|、|\/]+/,
                  },
                },
              },
            },
            {
              $addFields: {
                customizeHarm: {
                  $map: {
                    input: '$customizeHarm',
                    as: 'item',
                    in: '$$item.match',
                  },
                },
                harmFactors: {
                  $map: {
                    input: '$harmFactors',
                    as: 'item',
                    in: { $arrayElemAt: [ '$$item', 1 ] },
                  },
                },
              },
            },
            {
              $project: {
                mill: 1,
                workspace: 1,
                station: '$name',
                workType: '$workType',
                employeeCount: { $size: '$children' },
                harmFactors: {
                  $concatArrays: [ '$customizeHarm', '$harmFactors' ],
                },
              },
            },
          ];
          millStatistics = await ctx.service.db.aggregate(
            'MillConstruction',
            pipeline
          );
        } else {
          console.log('mills 数组为空，跳过 mill 统计查询');
        }

        return {
          id: EnterpriseID,
          harmStatistics: harmStatistics[0] ? harmStatistics[0].harmStatistics : [],
          stationStatistics: millStatistics,
        };
      })
    );
    return statisticsArr;
  }

  // 更新工作场所档案完成度-只要岗位上有人，那完成度就是100%
  async updateFilesCompleteness(enterpriseId) {
    const { ctx } = this;
    let EnterpriseID = ctx.session.adminUserInfo
      ? ctx.session.adminUserInfo.EnterpriseID
      : '';
    EnterpriseID = enterpriseId || EnterpriseID;
    const pipeline = [
      { $match: { EnterpriseID } },
      { $unwind: '$children' },
      {
        $addFields: {
          stations: {
            $cond: [
              { $eq: [ '$category', 'mill' ] },
              '$children.children',
              [ '$children' ],
            ],
          },
        },
      },
      { $project: { 'stations.children': 1 } },
      { $match: { 'stations.children.0': { $exists: true } } },
    ];
    const stations = await ctx.service.db.aggregate(
      'MillConstruction',
      pipeline
    );
    if (stations[0]) {
      // 更新档案完成度
      ctx.service.filesCompleteness.update({
        millConstruction: { completion: 100 },
      });
    } else {
      ctx.service.filesCompleteness.update({
        millConstruction: { completion: 0 },
      });
    }
  }

  // 获取统计数据
  async getStatistics(enterpriseId) {
    const { ctx } = this;
    let EnterpriseID = ctx.session.adminUserInfo
      ? ctx.session.adminUserInfo.EnterpriseID
      : '';
    if (enterpriseId) {
      EnterpriseID = enterpriseId;
    }
    const data = await ctx.service.db.find(
      'MillConstruction',
      { EnterpriseID },
      {},
      {
        populate: [
          { path: 'children.children.children.employees', select: 'status' },
          { path: 'children.children.employees', select: 'status' },
        ],
      }
    );
    const statistics = [];
    const allCount = {
      all: {
        value: 0,
        employee: [],
        label: '劳动者接害总人数',
        harmFactors: {},
      },
      seriousHarm: {
        value: 0,
        employee: [],
        label: '严重危害因素',
        harmFactors: {},
      },
      noSeriousHarm: {
        value: 0,
        employee: [],
        label: '一般危害因素',
        harmFactors: {},
      },
      chemical: {
        value: 0,
        employee: [],
        label: '化学有害因素',
        harmFactors: {},
      },
      dust: { value: 0, employee: [], label: '粉尘', harmFactors: {} },
      noise: { value: 0, employee: [], label: '噪声' },
      physical: {
        value: 0,
        employee: [],
        label: '其他物理因素',
        harmFactors: {},
      },
      radiation: {
        value: 0,
        employee: [],
        label: '放射性物质',
        harmFactors: {},
      },
      biological: { value: 0, employee: [], label: '生物', harmFactors: {} },
    };
    let item = {};
    for (let i = 0; i < data.length; i++) {
      item = data[i];
      item.countObj = await this.getPeopleCount([ item ], {
        all: {
          value: 0,
          employee: [],
          label: '劳动者接害总人数',
          harmFactors: {},
        },
        seriousHarm: {
          value: 0,
          employee: [],
          label: '严重危害因素',
          harmFactors: {},
        },
        noSeriousHarm: {
          value: 0,
          employee: [],
          label: '一般危害因素',
          harmFactors: {},
        },
        chemical: {
          value: 0,
          employee: [],
          label: '化学有害因素',
          harmFactors: {},
        },
        dust: { value: 0, employee: [], label: '粉尘', harmFactors: {} },
        noise: { value: 0, employee: [], label: '噪声' },
        physical: {
          value: 0,
          employee: [],
          label: '其他物理因素',
          harmFactors: {},
        },
        radiation: {
          value: 0,
          employee: [],
          label: '放射性物质',
          harmFactors: {},
        },
        biological: { value: 0, employee: [], label: '生物', harmFactors: {} },
      });
      // 去重，并统计人数
      item.countObj.all.employee = Array.from(
        new Set(item.countObj.all.employee)
      );
      item.countObj.all.value = item.countObj.all.employee.length;
      item.countObj.seriousHarm.employee = Array.from(
        new Set(item.countObj.seriousHarm.employee)
      );
      item.countObj.seriousHarm.value =
        item.countObj.seriousHarm.employee.length;
      item.countObj.noSeriousHarm.employee = Array.from(
        new Set(item.countObj.noSeriousHarm.employee)
      );
      item.countObj.noSeriousHarm.value =
        item.countObj.noSeriousHarm.employee.length;
      item.countObj.dust.employee = Array.from(
        new Set(item.countObj.dust.employee)
      );
      item.countObj.dust.value = item.countObj.dust.employee.length;
      item.countObj.chemical.employee = Array.from(
        new Set(item.countObj.chemical.employee)
      );
      item.countObj.chemical.value = item.countObj.chemical.employee.length;
      item.countObj.biological.employee = Array.from(
        new Set(item.countObj.biological.employee)
      );
      item.countObj.biological.value = item.countObj.biological.employee.length;
      item.countObj.radiation.employee = Array.from(
        new Set(item.countObj.radiation.employee)
      );
      item.countObj.radiation.value = item.countObj.radiation.employee.length;
      item.countObj.physical.employee = Array.from(
        new Set(item.countObj.physical.employee)
      );
      item.countObj.physical.value = item.countObj.physical.employee.length;
      item.countObj.noise.employee = Array.from(
        new Set(item.countObj.noise.employee)
      );
      item.countObj.noise.value = item.countObj.noise.employee.length;

      statistics.push({
        name: item.name,
        sort: 'item',
        count: Object.values(item.countObj),
      });
      allCount.all.employee = allCount.all.employee.concat(
        item.countObj.all.employee
      );
      allCount.dust.employee = allCount.dust.employee.concat(
        item.countObj.dust.employee
      );
      for (const key in item.countObj.dust.harmFactors) {
        if (!allCount.dust.harmFactors[key]) {
          allCount.dust.harmFactors[key] = [];
        }
        allCount.dust.harmFactors[key] = allCount.dust.harmFactors[key].concat(
          item.countObj.dust.harmFactors[key]
        );
      }
      allCount.chemical.employee = allCount.chemical.employee.concat(
        item.countObj.chemical.employee
      );
      for (const key in item.countObj.chemical.harmFactors) {
        if (!allCount.chemical.harmFactors[key]) {
          allCount.chemical.harmFactors[key] = [];
        }
        allCount.chemical.harmFactors[key] = allCount.chemical.harmFactors[
          key
        ].concat(item.countObj.chemical.harmFactors[key]);
      }
      allCount.biological.employee = allCount.biological.employee.concat(
        item.countObj.biological.employee
      );
      for (const key in item.countObj.biological.harmFactors) {
        if (!allCount.biological.harmFactors[key]) {
          allCount.biological.harmFactors[key] = [];
        }
        allCount.biological.harmFactors[key] = allCount.biological.harmFactors[
          key
        ].concat(item.countObj.biological.harmFactors[key]);
      }
      allCount.radiation.employee = allCount.radiation.employee.concat(
        item.countObj.radiation.employee
      );
      for (const key in item.countObj.radiation.harmFactors) {
        if (!allCount.radiation.harmFactors[key]) {
          allCount.radiation.harmFactors[key] = [];
        }
        allCount.radiation.harmFactors[key] = allCount.radiation.harmFactors[
          key
        ].concat(item.countObj.radiation.harmFactors[key]);
      }
      allCount.physical.employee = allCount.physical.employee.concat(
        item.countObj.physical.employee
      );
      for (const key in item.countObj.physical.harmFactors) {
        if (!allCount.physical.harmFactors[key]) {
          allCount.physical.harmFactors[key] = [];
        }
        allCount.physical.harmFactors[key] = allCount.physical.harmFactors[
          key
        ].concat(item.countObj.physical.harmFactors[key]);
      }
      allCount.noSeriousHarm.employee = allCount.noSeriousHarm.employee.concat(
        item.countObj.noSeriousHarm.employee
      );
      for (const key in item.countObj.noSeriousHarm.harmFactors) {
        if (!allCount.noSeriousHarm.harmFactors[key]) {
          allCount.noSeriousHarm.harmFactors[key] = [];
        }
        allCount.noSeriousHarm.harmFactors[key] =
          allCount.noSeriousHarm.harmFactors[key].concat(
            item.countObj.noSeriousHarm.harmFactors[key]
          );
      }
      allCount.seriousHarm.employee = allCount.seriousHarm.employee.concat(
        item.countObj.seriousHarm.employee
      );
      for (const key in item.countObj.seriousHarm.harmFactors) {
        if (!allCount.seriousHarm.harmFactors[key]) {
          allCount.seriousHarm.harmFactors[key] = [];
        }
        allCount.seriousHarm.harmFactors[key] =
          allCount.seriousHarm.harmFactors[key].concat(
            item.countObj.seriousHarm.harmFactors[key]
          );
      }
      allCount.noise.employee = allCount.noise.employee.concat(
        item.countObj.noise.employee
      );
    }

    // 总计去重
    allCount.all.employee = Array.from(new Set(allCount.all.employee));
    allCount.all.value = allCount.all.employee.length;
    allCount.dust.employee = Array.from(new Set(allCount.dust.employee));
    allCount.dust.harmFactors1 = [];
    for (const key in allCount.dust.harmFactors) {
      allCount.dust.harmFactors[key] = Array.from(
        new Set(allCount.dust.harmFactors[key])
      );
      allCount.dust.harmFactors[key][0] &&
        allCount.dust.harmFactors1.push({
          label: key,
          employee: allCount.dust.harmFactors[key],
          value: allCount.dust.harmFactors[key].length,
        });
    }
    allCount.dust.harmFactors = allCount.dust.harmFactors1;
    allCount.dust.value = allCount.dust.employee.length;

    allCount.chemical.employee = Array.from(
      new Set(allCount.chemical.employee)
    );
    allCount.chemical.value = allCount.chemical.employee.length;
    allCount.chemical.harmFactors1 = [];
    for (const key in allCount.chemical.harmFactors) {
      allCount.chemical.harmFactors[key] = Array.from(
        new Set(allCount.chemical.harmFactors[key])
      );
      allCount.chemical.harmFactors[key].length > 0 &&
        allCount.chemical.harmFactors1.push({
          label: key,
          employee: allCount.chemical.harmFactors[key],
          value: allCount.chemical.harmFactors[key].length,
        });
    }
    allCount.chemical.harmFactors = allCount.chemical.harmFactors1;

    allCount.biological.employee = Array.from(
      new Set(allCount.biological.employee)
    );
    allCount.biological.value = allCount.biological.employee.length;
    allCount.biological.harmFactors1 = [];
    for (const key in allCount.biological.harmFactors) {
      allCount.biological.harmFactors[key] = Array.from(
        new Set(allCount.biological.harmFactors[key])
      );
      allCount.biological.harmFactors[key].length > 0 &&
        allCount.biological.harmFactors1.push({
          label: key,
          employee: allCount.biological.harmFactors[key],
          value: allCount.biological.harmFactors[key].length,
        });
    }
    allCount.biological.harmFactors = allCount.biological.harmFactors1;

    allCount.radiation.employee = Array.from(
      new Set(allCount.radiation.employee)
    );
    allCount.radiation.value = allCount.radiation.employee.length;
    allCount.radiation.harmFactors1 = [];
    for (const key in allCount.radiation.harmFactors) {
      allCount.radiation.harmFactors[key] = Array.from(
        new Set(allCount.radiation.harmFactors[key])
      );
      allCount.radiation.harmFactors[key][0] &&
        allCount.radiation.harmFactors1.push({
          label: key,
          employee: allCount.radiation.harmFactors[key],
          value: allCount.radiation.harmFactors[key].length,
        });
    }
    allCount.radiation.harmFactors = allCount.radiation.harmFactors1;

    allCount.physical.employee = Array.from(
      new Set(allCount.physical.employee)
    );
    allCount.physical.value = allCount.physical.employee.length;
    allCount.physical.harmFactors1 = [];
    for (const key in allCount.physical.harmFactors) {
      allCount.physical.harmFactors[key] = Array.from(
        new Set(allCount.physical.harmFactors[key])
      );
      allCount.physical.harmFactors[key][0] &&
        allCount.physical.harmFactors1.push({
          label: key,
          employee: allCount.physical.harmFactors[key],
          value: allCount.physical.harmFactors[key].length,
        });
    }
    allCount.physical.harmFactors = allCount.physical.harmFactors1;

    allCount.noSeriousHarm.employee = Array.from(
      new Set(allCount.noSeriousHarm.employee)
    );
    allCount.noSeriousHarm.value = allCount.noSeriousHarm.employee.length;
    allCount.noSeriousHarm.harmFactors1 = [];
    for (const key in allCount.noSeriousHarm.harmFactors) {
      allCount.noSeriousHarm.harmFactors[key] = Array.from(
        new Set(allCount.noSeriousHarm.harmFactors[key])
      );
      allCount.noSeriousHarm.harmFactors[key][0] &&
        allCount.noSeriousHarm.harmFactors1.push({
          label: key,
          employee: allCount.noSeriousHarm.harmFactors[key],
          value: allCount.noSeriousHarm.harmFactors[key].length,
        });
    }
    allCount.noSeriousHarm.harmFactors = allCount.noSeriousHarm.harmFactors1;

    allCount.seriousHarm.employee = Array.from(
      new Set(allCount.seriousHarm.employee)
    );
    allCount.seriousHarm.value = allCount.seriousHarm.employee.length;
    allCount.seriousHarm.harmFactors1 = [];
    for (const key in allCount.seriousHarm.harmFactors) {
      allCount.seriousHarm.harmFactors[key] = Array.from(
        new Set(allCount.seriousHarm.harmFactors[key])
      );
      allCount.seriousHarm.harmFactors[key][0] &&
        allCount.seriousHarm.harmFactors1.push({
          label: key,
          employee: allCount.seriousHarm.harmFactors[key],
          value: allCount.seriousHarm.harmFactors[key].length,
        });
    }
    allCount.seriousHarm.harmFactors = allCount.seriousHarm.harmFactors1;

    allCount.noise.employee = Array.from(new Set(allCount.noise.employee));
    allCount.noise.value = allCount.noise.employee.length;
    statistics.push({
      name: '总计',
      sort: 'all',
      count: Object.values(allCount),
    });

    // 更新adminorg
    // await ctx.model.Adminorg.updateOne({ _id: EnterpriseID }, { $set: { harmStatistics: statistics } });
    await ctx.service.db.updateOne(
      'Adminorg',
      { _id: EnterpriseID },
      { $set: { harmStatistics: statistics } }
    );
  }
  // 判断是否是严重危害因素
  async isSeriousHarm(harmFactor) {
    return (
      (
        await this.ctx.service.db.findOne(
          'OccupationalexposureLimits',
          { chineseName: harmFactor },
          { seriousHarm: 1 }
        )
      ).seriousHarm === '1'
    );
  }

  // 获取接害人数
  async getPeopleCount(data, countObj) {
    for (let i = 0; i < data.length; i++) {
      if (data[i].category !== 'stations' && data[i].children) {
        countObj = await this.getPeopleCount(data[i].children, countObj);
      } else if (data[i].children) {
        if (data[i].harmFactors.length > 0 || data[i].customizeHarm) {
          data[i].children = data[i].children.filter(item => item.employees);
          countObj.all.employee = countObj.all.employee.concat(
            data[i].children.map(item => item.employees._id)
          );
          let item = {};
          for (let j = 0; j < data[i].harmFactors.length; j++) {
            item = data[i].harmFactors[j];
            data[i].children = data[i].children.filter(
              item => item.employees
            );
            if (await this.isSeriousHarm(item[1])) {
              // 严重危害因素
              if (!countObj.seriousHarm.harmFactors[item[1]]) {
                countObj.seriousHarm.harmFactors[item[1]] = [];
              }
              for (let k = 0; k < data[i].children.length; k++) {
                countObj.seriousHarm.employee.push(
                  data[i].children[k].employees._id
                );
                countObj.seriousHarm.harmFactors[item[1]].push(
                  data[i].children[k].employees._id
                );
              }
            } else {
              // 一般危害因素
              if (!countObj.noSeriousHarm.harmFactors[item[1]]) {
                countObj.noSeriousHarm.harmFactors[item[1]] = [];
              }
              for (let k = 0; k < data[i].children.length; k++) {
                countObj.noSeriousHarm.employee.push(
                  data[i].children[k].employees._id
                );
                countObj.noSeriousHarm.harmFactors[item[1]].push(
                  data[i].children[k].employees._id
                );
              }
            }
            if (item[0].indexOf('粉尘') !== -1) {
              if (!countObj.dust.harmFactors[item[1]]) {
                countObj.dust.harmFactors[item[1]] = [];
              }
              for (let k = 0; k < data[i].children.length; k++) {
                countObj.dust.employee.push(data[i].children[k].employees._id);
                countObj.dust.harmFactors[item[1]].push(
                  data[i].children[k].employees._id
                );
              }
            }
            if (item[0].indexOf('化学') !== -1) {
              if (!countObj.chemical.harmFactors[item[1]]) {
                countObj.chemical.harmFactors[item[1]] = [];
              }
              for (let k = 0; k < data[i].children.length; k++) {
                countObj.chemical.employee.push(
                  data[i].children[k].employees._id
                );
                countObj.chemical.harmFactors[item[1]].push(
                  data[i].children[k].employees._id
                );
              }
            }
            if (item[0].indexOf('生物') !== -1) {
              if (!countObj.biological.harmFactors[item[1]]) {
                countObj.biological.harmFactors[item[1]] = [];
              }
              for (let k = 0; k < data[i].children.length; k++) {
                countObj.biological.employee.push(
                  data[i].children[k].employees._id
                );
                countObj.biological.harmFactors[item[1]].push(
                  data[i].children[k].employees._id
                );
              }
            }
            if (item[0].indexOf('放射') !== -1) {
              if (!countObj.radiation.harmFactors[item[1]]) {
                countObj.radiation.harmFactors[item[1]] = [];
              }
              for (let k = 0; k < data[i].children.length; k++) {
                countObj.radiation.employee.push(
                  data[i].children[k].employees._id
                );
                countObj.radiation.harmFactors[item[1]].push(
                  data[i].children[k].employees._id
                );
              }
            }
            if (
              item[0].indexOf('物理') !== -1 &&
              item[1].indexOf('噪声') === -1
            ) {
              if (!countObj.physical.harmFactors[item[1]]) {
                countObj.physical.harmFactors[item[1]] = [];
              }
              for (let k = 0; k < data[i].children.length; k++) {
                countObj.physical.employee.push(
                  data[i].children[k].employees._id
                );
                countObj.physical.harmFactors[item[1]].push(
                  data[i].children[k].employees._id
                );
              }
            }
            if (item[1].indexOf('噪声') !== -1) {
              for (let k = 0; k < data[i].children.length; k++) {
                countObj.noise.employee.push(data[i].children[k].employees._id);
              }
            }
          }
        }
      }
    }
    return countObj;
  }

  // 根据危害因素名称数组匹配危害因素
  // async matchHarmFactors(harmFactors) {
  //   let arr = [];// 匹配到的结果
  //   let matchHarms = [];
  //   let customHamrs = [];
  //   let dyadicArray = false;
  //   if (harmFactors[0]) {
  //     if (harmFactors[0] instanceof Array) {
  //       dyadicArray = true;
  //       harmFactors = harmFactors.map(item => item[1]);
  //     } else {
  //       harmFactors = harmFactors.map(item => {
  //         item = item.replace(/（总尘）|（总）|\(总\)|（呼）|\(呼\)|（总粉尘）|\(总粉尘\)|（呼吸性粉尘）|\(呼吸性粉尘\)|（呼尘）|\(总尘\)|\(呼尘\)/gi, '');
  //         return item;
  //       });
  //     }
  //     const res = await this.ctx.model.OccupationalexposureLimits.aggregate([
  //       { $unwind: '$chineseName' },
  //       { $match: { chineseName: { $in: harmFactors } } },
  //     ]);
  //     if (dyadicArray) { // 二维数组
  //       res.forEach(item => {
  //         arr.push(JSON.stringify([ item.catetory, item.showName ]));
  //       });
  //     } else {
  //       res.forEach(item => {
  //         matchHarms = matchHarms.concat(item.chineseName);
  //         arr.push(JSON.stringify([ item.catetory, item.showName ]));
  //       });
  //       // 差集
  //       customHamrs = harmFactors.filter(item => !(new Set(matchHarms)).has(item));
  //     }
  //   }
  //   arr = Array.from(new Set(arr));
  //   return { harmFactors: arr.map(item => JSON.parse(item)), customHamrs: customHamrs.join('、') };
  // }
  async matchHarmFactors(harmFactors) {
    console.log('matchHarmFactors 被调用，参数:', harmFactors);

    const arr = []; // 匹配到的结果
    const matchHarms = [];
    let customHamrs = [];

    // 检查 harmFactors 是否为空或无效
    if (!harmFactors || !Array.isArray(harmFactors) || harmFactors.length === 0) {
      console.log('harmFactors 为空或无效，返回空结果');
      return { harmFactors: arr, customHamrs: customHamrs.join('、') };
    }

    if (harmFactors[0]) {
      if (harmFactors[0] instanceof Array) {
        harmFactors = harmFactors.map(item => item[1]);
      } else {
        harmFactors = harmFactors.map(item => {
          item = item.replace(
            /（总尘）|（总）|\(总\)|（呼）|\(呼\)|（总粉尘）|\(总粉尘\)|（呼吸性粉尘）|\(呼吸性粉尘\)|（呼尘）|\(总尘\)|\(呼尘\)/gi,
            ''
          );
          return item;
        });
      }

      // 过滤掉空字符串
      harmFactors = harmFactors.filter(item => item && item.trim() !== '');

      if (harmFactors.length === 0) {
        console.log('过滤后 harmFactors 为空，返回空结果');
        return { harmFactors: arr, customHamrs: customHamrs.join('、') };
      }

      console.log('处理后的 harmFactors:', harmFactors);

      const pipeline = [
        { $unwind: '$chineseName' },
        { $match: { chineseName: { $in: harmFactors } } },
      ];

      console.log('执行 aggregate 查询...');
      const res = await this.ctx.service.db.aggregate(
        'OccupationalexposureLimits',
        pipeline
      );
      if (harmFactors[0] instanceof Array) {
        res.forEach(item => {
          arr.push([ item.catetory, item.showName ]);
        });
      } else {
        res.forEach(item => {
          matchHarms.push(item.showName);
          arr.push([ item.catetory, item.showName ]);
        });
        // 差集
        customHamrs = harmFactors.filter(
          item => !new Set(matchHarms).has(item)
        );
      }
    }
    return { harmFactors: arr, customHamrs: customHamrs.join('、') };
  }

  async addWorkSpace(params) {
    try {
      params.workspace.category = 'workspaces';
      // const res = await this.ctx.model.MillConstruction.updateOne({ _id: params.millId }, { $addToSet: { children: params.workspace } });
      const mill = await this.ctx.service.db.findOne('MillConstruction', {
        _id: params.millId,
      });
      const exists = mill.children.some(
        child =>
          child.category === params.workspace.category &&
          child.name === params.workspace.name
      );

      if (!exists) {
        await this.ctx.service.db.updateOne(
          'MillConstruction',
          { _id: params.millId },
          { $addToSet: { children: params.workspace } }
        );
        return '添加成功';
      }
      throw new Error('该车间已存在');
    } catch (error) {
      console.log(error.message);
      return 500;
    }
  }

  async addStation(params) {
    const { ctx } = this;
    try {
      let stationId = '';
      let EnterpriseID = ctx.session.adminUserInfo
        ? ctx.session.adminUserInfo.EnterpriseID
        : '';
      EnterpriseID = params.enterpriseId || EnterpriseID;
      // let employees = await ctx.model.Employee.find({ EnterpriseID, station: params.station.name });
      const departs = [];
      let employees = [];
      const employees2 = [];
      let departOne = {};
      let departTwo = {};
      // const departThree = {};
      let query = {};
      let setFiled = {};
      params.station.category = 'stations';
      if (params.millId) {
        // 查询层级匹配的部门
        // departThree = await ctx.model.Dingtree.find({ EnterpriseID, name: params.station.name });
        // for (let i = 0; i < departThree.length; i++) {
        departTwo = await ctx.service.db.find('Dingtree', {
          EnterpriseID,
          name: params.workspaceName,
        });
        for (let j = 0; j < departTwo.length; j++) {
          // if (departTwo[j].name === params.workspaceName) {
          departOne = await ctx.service.db.find('Dingtree', {
            EnterpriseID,
            _id: departTwo[j].parentid,
          });
          for (let k = 0; k < departOne.length; k++) {
            if (departOne[k].name === params.millName) {
              departs.push([ departOne[k]._id, departTwo[j]._id ]);
            }
          }
          // }
        }
        // }
        query = { _id: params.millId, 'children._id': params.workspaceId };
        setFiled = 'children.$.children';
      } else {
        // departThree = await ctx.model.Dingtree.find({ EnterpriseID, name: params.station.name });
        // for (let i = 0; i < departThree.length; i++) {
        departTwo = await ctx.service.db.find('Dingtree', {
          EnterpriseID,
          name: params.workspaceName,
        });
        for (let j = 0; j < departTwo.length; j++) {
          // if (departTwo[j].name === params.workspaceName) {
          departs.push([ departTwo[j]._id ]);
          // }
        }
        // }
        query = { _id: params.workspaceId };
        setFiled = 'children';
      }
      for (let i = 0; i < departs.length; i++) {
        const pipeline = [
          {
            $match: {
              EnterpriseID,
              station: params.station.name,
              enable: true,
            },
          },
          { $unwind: '$departs' },
          { $project: { departs: 1, isArr: { $isArray: '$departs' } } },
          { $match: { isArr: true, 'departs.0': { $exists: true } } },
          { $project: { isIn: { $setIsSubset: [ departs[i], '$departs' ] } } },
          { $match: { isIn: true } },
          { $group: { _id: '$_id' } },
        ];
        employees = await ctx.service.db.aggregate('Employee', pipeline);
        employees = JSON.parse(JSON.stringify(employees));
        // for (let j = 0; j < employees.length; j++) {
        //   employees2.push(employees[j]._id);
        // }
      }
      employees = Array.from(
        new Set(employees.map(item => JSON.stringify(item)))
      );
      // employees = employees.map(item => JSON.parse(item));
      // const employeeIds = employees2;
      employees.map(item => {
        item = JSON.parse(item);
        employees2.push({ employees: item._id });
        return item;
      });
      params.station.children =
        params.station.children && params.station.children.concat(employees2);
      // const updateRes = await ctx.model.MillConstruction.updateOne(query, { $addToSet: { [setFiled]: params.station } });
      // 查询是否存在
      let mill;
      let exists = false;
      if (params.millId) {
        mill = await ctx.service.db.findOne('MillConstruction', {
          _id: params.millId,
        });
        exists = mill.children.some(child => {
          if (child.name === params.workspaceName) {
            if (child.children && Array.isArray(child.children)) {
              return child.children.some(
                item => item.name === params.station.name
              );
            }
            return false;
          }
          return false;
        });
      } else {
        mill = await ctx.service.db.findOne('MillConstruction', {
          _id: params.workspaceId,
        });
        exists = mill.children.some(
          child =>
            child.category === params.station.category &&
            child.name === params.station.name
        );
      }
      if (exists) {
        throw new Error('该岗位已存在');
      }
      const updateRes = await ctx.service.db.updateOne(
        'MillConstruction',
        query,
        { $addToSet: { [setFiled]: params.station } }
      );
      if (updateRes.nModified > 0) {
        let res = {};
        if (params.millId) {
          const pipeline = [
            { $match: { _id: params.millId } },
            { $unwind: '$children' },
            { $match: { 'children._id': params.workspaceId } },
            { $unwind: '$children.children' },
            { $match: { 'children.children.name': params.station.name } },
          ];
          res = await ctx.service.db.aggregate('MillConstruction', pipeline);
          stationId = res[0].children.children._id;
        } else {
          const pipeline = [
            { $match: { _id: params.workspaceId } },
            { $unwind: '$children' },
            { $match: { 'children.name': params.station.name } },
          ];
          res = await ctx.service.db.aggregate('MillConstruction', pipeline);
          stationId = res[0] && res[0].children._id;
        }
        // 更新工作状态变更
        for (let i = 0; i < employees.length; i++) {
          // await ctx.model.EmployeeStatusChange.updateMany({ employee: employees[i] }, { $push: { statusChanges: {
          //   changType: 2,
          //   EnterpriseID,
          //   timestamp: employees[i].workStart,
          //   stationsTo: [ stationId ],
          //   message: '转岗',
          // } } });
          await ctx.service.db.updateMany(
            'EmployeeStatusChange',
            { employee: employees[i] },
            {
              $push: {
                statusChanges: {
                  changType: 2,
                  EnterpriseID,
                  timestamp: employees[i].workStart,
                  stationsTo: [ stationId ],
                  message: '转岗',
                },
              },
            }
          );
        }

        // 更新档案完成度
        this.updateFilesCompleteness(EnterpriseID);
        // this.getStatistics(EnterpriseID);
      }
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async findMillConstruction(params) {
    try {
      const { ctx } = this;
      // console.log(params.EnterpriseID, 'iddddddddddddddddddddddd');
      const { EnterpriseID } = params;
      const enterpriseIds = await ctx.helper.getScopeData('enterprise_ids') || [];
      const res = await this.ctx.service.db.find(
        'MillConstruction',
        { EnterpriseID: { $in: enterpriseIds } },
        {},
        {
          populate: [
            { path: 'children.children.children.employees', select: '_id name status EnterpriseID unitCode' },
            { path: 'children.children.employees', select: '_id name status EnterpriseID unitCode' },
          ],
          sort: { sortIndex: 1 },
        }
      );
      if (enterpriseIds.length === 1) {
        return res;
      }
      let list =
        (await this.ctx.service.db.find(
          'Adminorg',
          { _id: { $in: enterpriseIds } },
          { _id: 1, shortName: 1, cname: 1 },
          { sort: { unitCode: 1 } }
        )) || [];
      list = JSON.parse(JSON.stringify(list));
      const result = [];
      list.forEach(item => {
        const matchingItems = res.filter(resItem => resItem.EnterpriseID === item._id);
        if (!matchingItems.length) return;
        // 创建一个新对象包含企业信息和子节点
        const enterpriseNode = {
          name: item.shortName || item.cname,
          children: matchingItems,
          category: 'enterprises',
          id: item._id,
          _id: item._id,
          ...(item._id === EnterpriseID && { isGroup: true }),
        };
        result.push(enterpriseNode);
      });
      return result;
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async findMillByEnterprise(params) {
    try {
      const { EnterpriseID } = params;
      const res = await this.ctx.service.db.find(
        'MillConstruction',
        { EnterpriseID },
        '_id name children',
        { lean: true }
      );
      const result = [];
      for (let i = 0; i < res.length; i++) {
        const ele = res[i];
        if (!ele.name) continue;
        const children = [];
        for (let j = 0; j < ele.children.length; j++) {
          const child = ele.children[j];
          if (!child.name) continue;
          const subChildren = [];
          for (let k = 0; k < child.children.length; k++) {
            const subChild = child.children[k];
            if (!subChild.name) continue;
            subChildren.push({
              value: subChild._id,
              label: subChild.name,
            });
          }
          children.push({
            value: child._id,
            label: child.name,
            children: subChildren.length ? subChildren : undefined,
          });
        }
        result.push({
          value: ele._id,
          label: ele.name,
          children,
        });
      }
      return result;
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  // 查询防护用具
  async protectiveEquipment(params) {
    const { ctx } = this;
    try {
      const res = [];
      for (let i = 0; i < params.length - 1; i++) {
        let con = await ctx.service.db.findOne(
          'OccupationalexposureLimits',
          { chineseName: params[i][1] },
          { protectiveEquipment: 1, chineseName: 1 }
        );
        let con2 = await ctx.Service.db.findOne(
          'OccupationalexposureLimits',
          { chineseName: params[i][1] },
          { protectiveEquipment: 1, chineseName: 1 }
        );
        con = tools.convertToEditJson(con);
        con2 = tools.convertToEditJson(con2);
        res.push(con ? con.protectiveEquipment : '');
        res.push(con2 ? con2.protectiveEquipment : '');
      }
      const res1 = Array.from(new Set(res));
      return res1;
    } catch (error) {
      console.log(error);
      return 500;
    }
  }
  // 查询员工
  async findEmployees(params) {
    const { ctx } = this;
    const match = {};
    if (params.millId) {
      const millRes = await ctx.service.db.find('MillConstruction', {
        $or: [
          { _id: params.millId }, // 检查第二层的 employees
          { 'children._id': params.millId }, // 检查第二层的 employees
          { 'children.children._id': params.millId }, // 检查第三层的 employees
        ],
      });
      const employeeIds = this.extractEmployeeIds(millRes[0] || {});
      if (employeeIds.length > 0) {
        match._id = {
          $in: employeeIds,
        };
      } else {
        // 如果没有员工ID，返回空结果
        match._id = { $in: [ 'nonexistent' ] };
      }
    }
    if (params.name) {
      match.$or = [
        { name: new RegExp(params.name.trim(), 'i') },
        { idCard: new RegExp(params.name.trim(), 'i') },
        { unitCode: new RegExp(params.name.trim(), 'i') },
      ];
    }
    if (params.enterpriseId) {
      match.EnterpriseID = params.enterpriseId;
    }
    console.log(match, 'match');
    const res = await ctx.service.db.find(
      'Employee',
      {
        ...match,
        status: 1,
        enable: true,
      },
      { name: 1, unitCode: 1, _id: 1 }
    );
    return res;
  }
  // 将children.childre
  extractEmployeeIds(data) {
    const employeeIds = [];
    function traverse(node) {
      if (node.employees) {
        employeeIds.push(node.employees);
      }
      if (node.children && Array.isArray(node.children)) {
        node.children.forEach(child => traverse(child));
      }
    }

    traverse(data);
    return employeeIds;
  }
  async addEmployee(params) {
    try {
      const { ctx } = this;
      let EnterpriseID = ctx.session.adminUserInfo
        ? ctx.session.adminUserInfo.EnterpriseID
        : '';
      EnterpriseID = params.enterpriseId || EnterpriseID;
      // 处理需要删除的员工
      if (params.deleteEmployeeIds && params.deleteEmployeeIds.length > 0) {
        // 更新员工状态变更记录
        const statusChange = {
          changType: 2,
          EnterpriseID,
          stationFrom: params.stationId,
          message: '转岗',
          timestamp: new Date(),
        };

        await ctx.service.db.updateMany(
          'EmployeeStatusChange',
          { employee: { $in: params.deleteEmployeeIds } },
          { $addToSet: { statusChanges: statusChange } }
        );

        if (params.millId) {
          await ctx.service.db.updateOne(
            'MillConstruction',
            { _id: params.millId },
            {
              $pull: {
                'children.$[i].children.$[j].children': {
                  employees: { $in: params.deleteEmployeeIds },
                },
              },
            },
            {
              arrayFilters: [
                { 'i._id': params.workspaceId },
                { 'j._id': params.stationId },
              ],
            }
          );
        } else {
          await ctx.service.db.updateOne(
            'MillConstruction',
            { _id: params.workspaceId, 'children._id': params.stationId },
            {
              $pull: {
                'children.$.children': {
                  employees: { $in: params.deleteEmployeeIds },
                },
              },
            }
          );
        }
      }

      // 处理需要添加的员工
      if (params.addedEmployeeIds && params.addedEmployeeIds.length > 0) {

        // 判断新增的员工是否已经存在于该公司的任何岗位
        if (ctx.app.config.isRepeatAddEmployee === '0') {
          let pipeline = [];
          // 如果已经重复，则不添加
          if (params.millId) {
            pipeline = [
              {
                $match: {
                  EnterpriseID,
                },
              },
              {
                $unwind: '$children',
              },
              {
                $unwind: '$children.children',
              },
              {
                $replaceRoot: {
                  newRoot: {
                    children: '$children.children.children',
                  },
                },
              },
              {
                $project: {
                  'children.employees': 1,
                },
              },
            ];
          } else {
            pipeline = [
              {
                $match: {
                  EnterpriseID,
                },
              },
              {
                $unwind: '$children',
              },
              {
                $replaceRoot: {
                  newRoot: {
                    children: '$children.children',
                  },
                },
              },
              {
                $project: {
                  'children.employees': 1,
                },
              },
            ];
          }
          const mills = await ctx.service.db.aggregate('MillConstruction', pipeline, {
            allowDiskUse: true,
          });
          console.log(mills, 'mills');
          if (mills.length > 0 && mills[0].children.length > 0) {
            const employeeIds = mills.map(item =>
              item.children.map(item => item.employees)
            ).flat();
            console.log(employeeIds, 'employeeIds');
            const isRepeat = params.addedEmployeeIds.some(id => employeeIds.includes(id));
            if (isRepeat) {
              throw new Error('员工已存在');
            }
          }
        }
        // 更新员工状态变更记录
        for (let i = 0; i < params.addedEmployeeIds.length; i++) {
          await ctx.service.db.updateMany(
            'EmployeeStatusChange',
            { employee: params.addedEmployeeIds[i] },
            {
              $addToSet: {
                statusChanges: {
                  changType: 2,
                  EnterpriseID,
                  timestamp: new Date(),
                  stationsTo: [ params.stationId ],
                  message: '转岗',
                },
              },
            }
          );
        }

        // 向岗位添加员工
        const addEmployees = params.addedEmployeeIds.map(id => ({
          employees: id,
          isPass: '0',
        }));

        if (params.millId) {
          await ctx.service.db.updateOne(
            'MillConstruction',
            { _id: params.millId },
            {
              $push: {
                'children.$[i].children.$[j].children': {
                  $each: addEmployees,
                },
              },
            },
            {
              arrayFilters: [
                { 'i._id': params.workspaceId },
                { 'j._id': params.stationId },
              ],
            }
          );
        } else {
          await ctx.service.db.updateOne(
            'MillConstruction',
            { _id: params.workspaceId, 'children._id': params.stationId },
            {
              $push: {
                'children.$.children': {
                  $each: addEmployees,
                },
              },
            }
          );
        }
      }

      return '员工更新成功';
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async editMill(params) {
    const updateData = {
      status: params.status || '1',
    };

    // 添加编码字段的支持
    if (params.unitCode !== undefined || typeof params.encode !== 'undefined') {
      const checkResult = await this.checkUnitCodeDuplicate(params.unitCode, {
        enterpriseId: params.enterpriseId,
        millId: params.millId,
        workspaceId: params.workspaceId,
      });
      if (!checkResult.success) {
        throw new Error(checkResult.message);
      }
      updateData.unitCode = params.unitCode;
      updateData.encode = params.encode;
    }
    if (params.name !== undefined) {
      updateData.name = params.name;
    }
    const res = await this.ctx.service.db.updateOne(
      'MillConstruction',
      { _id: params.millId },
      { $set: updateData }
    );
    if (res.nModified > 0) {
      // if (!params.status) await this.getStatistics(EnterpriseID);
      return '修改成功';
    }
  }

  async editWorkspace(params) {
    // const res = await this.ctx.model.MillConstruction.updateOne({ _id: params.millId, 'children._id': params.workspaceId }, { $set: { 'children.$.name': params.name, 'children.$.status': params.status || '1' } });
    const updateData = {
      'children.$.status': params.status || '1',
    };

    // 添加编码字段的支持
    if (params.unitCode) {
      const checkResult = await this.checkUnitCodeDuplicate(params.unitCode, {
        enterpriseId: params.enterpriseId,
        millId: params.millId,
        workspaceId: params.workspaceId,
      });
      if (!checkResult.success) {
        throw new Error(checkResult.message);
      }
      updateData['children.$.unitCode'] = params.unitCode;
    }
    if (params.encode) {
      updateData['children.$.encode'] = params.encode;
    }
    if (params.name) {
      updateData['children.$.name'] = params.name;
    }
    console.log(updateData, 'updateData');
    const res = await this.ctx.service.db.updateOne(
      'MillConstruction',
      { _id: params.millId, 'children._id': params.workspaceId },
      { $set: updateData }
    );
    if (res.nModified > 0) {
      return '修改成功';
    }

  }

  async editStation(params) {


    try {
      const { ctx } = this;
      // let EnterpriseID = ctx.session.adminUserInfo
      //   ? ctx.session.adminUserInfo.EnterpriseID
      //   : '';
      // EnterpriseID = params.enterpriseId || EnterpriseID;

      // 添加编码字段的支持
      if (params.unitCode !== undefined) {
        let condition = { };
        if (params.millId) {
          condition = {
            enterpriseId: params.enterpriseId,
            millId: params.millId,
            workspaceId: params.workspaceId,
            stationId: params.stationId,
          };
        } else {
          condition = {
            enterpriseId: params.enterpriseId,
            millId: params.workspaceId,
            workspaceId: params.stationId,
          };
        }
        const checkResult = await this.checkUnitCodeDuplicate(params.unitCode, condition);
        console.log(checkResult, 'checkResult====>');

        if (!checkResult.success) {
          throw new Error(checkResult.message);

        }

      }
      console.log(2222);
      if (params.harmFactors) {
        const harms = await this.matchHarmFactors(
          params.customizeHarm
            ? (params.customizeHarm.trim() + '').split(/,|，|、|\s+/gi)
            : []
        );
        params.harmFactors = (
          await this.matchHarmFactors(params.harmFactors)
        ).harmFactors;
        params.harmFactors = params.harmFactors.concat(harms.harmFactors);
        params.customizeHarm = harms.customHamrs;
      }
      delete params.children;
      const params2 = {};
      if (params.millId) {
        for (const key in params) {
          if (
            key === 'millId' ||
            key === 'workspaceId' ||
            key === 'stationId' ||
            key === 'unitCode' // 特殊处理unitCode字段
          ) {
            // 不作为children的属性处理
            if (key === 'unitCode' || key === 'encode') {
              params2['children.$[i].children.$[j].unitCode'] = params.unitCode;
            }
          } else {
            params2['children.$[i].children.$[j].' + key] = params[key];
          }
        }
        // await ctx.model.MillConstruction.updateOne({ _id: params.millId }, { $set: params2 },
        // {
        //   arrayFilters: [
        //     { 'i._id': params.workspaceId },
        //     { 'j._id': params.stationId },
        //   ],
        // });
        await ctx.service.db.updateOne(
          'MillConstruction',
          { _id: params.millId },
          { $set: params2 },
          {
            arrayFilters: [
              { 'i._id': params.workspaceId },
              { 'j._id': params.stationId },
            ],
          }
        );
      } else {
        for (const key in params) {
          if (key === 'workspaceId' || key === 'stationId' || key === 'unitCode' || key === 'encode') {
            // 特殊处理unitCode字段
            if (key === 'unitCode') {
              params2['children.$[i].unitCode'] = params.unitCode;
            }
          } else {
            params2['children.$[i].' + key] = params[key];
          }
        }
        // await ctx.model.MillConstruction.updateOne({ _id: params.workspaceId }, { $set: params2 },
        //   {
        //     arrayFilters: [
        //       { 'i._id': params.stationId },
        //     ],
        //   });
        await ctx.service.db.updateOne(
          'MillConstruction',
          { _id: params.workspaceId },
          { $set: params2 },
          {
            arrayFilters: [{ 'i._id': params.stationId }],
          }
        );
      }
      // await this.getStatistics(EnterpriseID);
      return '修改成功';
    } catch (error) {
      console.log(error);
      return {
        message: error.message,
        status: 500,
      };
    }
  }
  /**
   * 检查指定的unitCode是否在任何层级已存在
   * @param {String} unitCode - 要检查的编码
   * @param {Object} excludeIds - 需要排除的ID {millId, workspaceId, stationId}
   * @return {Object} - 验证结果 {success: boolean, message: string}
   */
  async checkUnitCodeDuplicate(unitCode, excludeIds = {}) {
    console.log(unitCode, excludeIds, 'unitCode, excludeIds');
    if (!unitCode) {
      return {
        success: true,
        message: '编码为空，无需验证',
      };
    }

    const { ctx } = this;

    // 确定当前操作的实体类型
    const isEditingWorkspace = excludeIds.workspaceId && !excludeIds.stationId;
    const isEditingStation = excludeIds.stationId;
    const isEditingMill = excludeIds.millId && !isEditingWorkspace && !isEditingStation;

    // 检查厂房级别
    // 只有在修改厂房时才排除该厂房自身，修改车间或岗位时不排除任何厂房
    const millExcludeCondition = isEditingMill ? { _id: { $ne: excludeIds.millId } } : {};
    const workspaceExcludeCondition = isEditingWorkspace ? { 'children._id': { $ne: excludeIds.workspaceId } } : {};

    const existingMill = await ctx.service.db.findOne('MillConstruction', {
      unitCode,
      ...millExcludeCondition,
    });

    if (existingMill) {
      return {
        success: false,
        message: `厂房编码 ${unitCode} 已存在`,
      };
    }

    // 检查车间级别
    const pipeline = [
      { $match: { 'children.unitCode': unitCode } },
      { $unwind: '$children' },
      {
        $match: {
          'children.unitCode': unitCode,
          ...workspaceExcludeCondition,
        },
      },
    ];
    console.log(pipeline);
    const existingWorkspace = await ctx.service.db.aggregate('MillConstruction', pipeline);
    if (existingWorkspace && existingWorkspace.length > 0) {
      return {
        success: false,
        message: `车间编码 ${unitCode} 已存在`,
      };
    }

    // 检查岗位级别
    const pipeline2 = [
      { $match: { 'children.children.unitCode': unitCode } },
      { $unwind: '$children' },
      { $unwind: '$children.children' },
      {
        $match: {
          'children.children.unitCode': unitCode,
          ...(isEditingStation ? { 'children.children._id': { $ne: excludeIds.stationId } } : {}),
        },
      },
    ];
    const existingStation = await ctx.service.db.aggregate('MillConstruction', pipeline2);

    if (existingStation && existingStation.length > 0) {
      return {
        success: false,
        message: `岗位编码 ${unitCode} 已存在`,
      };
    }

    return {
      success: true,
      message: '编码验证通过',
    };
  }
  async deleteStation(params) {
    try {
      const { ctx } = this;
      let EnterpriseID = ctx.session.adminUserInfo
        ? ctx.session.adminUserInfo.EnterpriseID
        : '';
      EnterpriseID = params.enterpriseId || EnterpriseID;
      let res = {};
      if (params.millId) {
        // res = await ctx.model.MillConstruction.updateOne({ _id: params.millId, 'children._id': params.workspaceId }, { $pull: { 'children.$.children': { _id: params.stationId } } });
        res = await ctx.service.db.updateOne(
          'MillConstruction',
          { _id: params.millId, 'children._id': params.workspaceId },
          { $pull: { 'children.$.children': { _id: params.stationId } } }
        );
      } else {
        // res = await ctx.model.MillConstruction.updateOne({ _id: params.workspaceId }, { $pull: { children: { _id: params.stationId } } });
        res = await ctx.service.db.updateOne(
          'MillConstruction',
          { _id: params.workspaceId },
          { $pull: { children: { _id: params.stationId } } }
        );
      }
      // 更新档案完成度
      this.updateFilesCompleteness(EnterpriseID);
      if (res.nModified > 0) {
        return '删除成功';
      }
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async deleteWorkspace(params) {
    try {
      const { ctx } = this;
      let EnterpriseID = ctx.session.adminUserInfo
        ? ctx.session.adminUserInfo.EnterpriseID
        : '';
      EnterpriseID = params.enterpriseId || EnterpriseID;
      if (params.millId) {
        // await ctx.model.MillConstruction.updateOne({ _id: params.millId }, { $pull: { children: { _id: params.workspaceId } } });
        await ctx.service.db.updateOne(
          'MillConstruction',
          { _id: params.millId },
          { $pull: { children: { _id: params.workspaceId } } }
        );
      } else {
        // await ctx.model.MillConstruction.deleteOne({ _id: params.workspaceId });
        await ctx.service.db.deleteOne('MillConstruction', {
          _id: params.workspaceId,
        });
      }
      // 更新档案完成度
      this.updateFilesCompleteness(EnterpriseID);
      // await this.getStatistics(EnterpriseID);
      return '删除成功';
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async deleteMill(params) {
    try {
      const { ctx } = this;
      let EnterpriseID = ctx.session.adminUserInfo
        ? ctx.session.adminUserInfo.EnterpriseID
        : '';
      if (params.nodeIds) {
        await ctx.service.db.deleteMany('MillConstruction', {
          EnterpriseID: { $in: params.nodeIds },
        });
      } else {
        EnterpriseID = params.enterpriseId || EnterpriseID;
        // await ctx.model.MillConstruction.deleteMany({ _id: { $in: params.millId } });
        await ctx.service.db.deleteMany('MillConstruction', {
          _id: { $in: params.millId },
        });
      }
      // 更新档案完成度
      this.updateFilesCompleteness(EnterpriseID);
      // await this.getStatistics(EnterpriseID);
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async deleteEmployee(params, noEmployeeWorkChang = false) {
    let res = {};
    try {
      const { ctx } = this;
      let EnterpriseID = ctx.session.adminUserInfo
        ? ctx.session.adminUserInfo.EnterpriseID
        : '';
      const enterpriseId = await ctx.service.db.findOne(
        'Employee',
        { _id: params.employeeId },
        { EnterpriseID: 1 }
      );
      EnterpriseID = enterpriseId.EnterpriseID || EnterpriseID;
      if (params.millId) {
        // res = await ctx.model.MillConstruction.updateOne({ _id: params.millId }, { $pull: { 'children.$[i].children.$[j].children': { employees: params.employeeId } } },
        // {
        //   arrayFilters: [
        //     { 'i._id': params.workspaceId },
        //     { 'j._id': params.stationId },
        //   ],
        // });
        res = await ctx.service.db.updateOne(
          'MillConstruction',
          { _id: params.millId },
          {
            $pull: {
              'children.$[i].children.$[j].children': {
                employees: params.employeeId,
              },
            },
          },
          {
            arrayFilters: [
              { 'i._id': params.workspaceId },
              { 'j._id': params.stationId },
            ],
          }
        );
      } else {
        // res = await ctx.model.MillConstruction.updateOne({ _id: params.workspaceId }, { $pull: { 'children.$[i].children': { employees: params.employeeId } } },
        // {
        //   arrayFilters: [
        //     { 'i._id': params.stationId },
        //   ],
        // });
        res = await ctx.service.db.updateOne(
          'MillConstruction',
          { _id: params.workspaceId },
          {
            $pull: {
              'children.$[i].children': { employees: params.employeeId },
            },
          },
          {
            arrayFilters: [{ 'i._id': params.stationId }],
          }
        );
      }
      if (noEmployeeWorkChang) {
        if (res.nModified > 0) {
          return '删除成功';
        }
      }
      // const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
      const statusChange = {
        changType: 2,
        EnterpriseID,
        stationFrom: params.stationId,
        message: '转岗',
      };
      // await ctx.model.EmployeeStatusChange.updateOne({ employee: params.employeeId }, { $addToSet: { statusChanges: statusChange } });
      await ctx.service.db.updateOne(
        'EmployeeStatusChange',
        { employee: params.employeeId },
        { $addToSet: { statusChanges: statusChange } }
      );
      if (res.nModified > 0) {
        // 更新档案完成度
        this.updateFilesCompleteness(EnterpriseID);
        // await this.getStatistics(EnterpriseID);
        return '删除成功';
      }
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async dropUpdateEmployee(params) {
    try {
      // const res = await this.ctx.model.MillConstruction.updateOne({ _id: params.millId }, { $set: { 'children.$[i].children.$[j].children': params.employeeIds } },
      // {
      //   arrayFilters: [
      //     { 'i._id': params.workspaceId },
      //     { 'j._id': params.stationId },
      //   ],
      // });
      const res = await this.ctx.service.db.updateOne(
        'MillConstruction',
        { _id: params.millId },
        {
          $set: { 'children.$[i].children.$[j].children': params.employeeIds },
        },
        {
          arrayFilters: [
            { 'i._id': params.workspaceId },
            { 'j._id': params.stationId },
          ],
        }
      );
      if (res.nModified > 0) {
        return '更新成功';
      }
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async dropUpdateStation(params) {
    try {
      // const res = await this.ctx.model.MillConstruction.updateOne({ _id: params.millId }, { $set: { 'children.$[i].children': params.stations } },
      // {
      //   arrayFilters: [
      //     { 'i._id': params.workspaceId },
      //   ],
      // });
      const res = await this.ctx.service.db.updateOne(
        'MillConstruction',
        { _id: params.millId },
        { $set: { 'children.$[i].children': params.stations } },
        {
          arrayFilters: [{ 'i._id': params.workspaceId }],
        }
      );
      if (res.nModified > 0) {
        return '更新成功';
      }
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async dropUpdateWorkspace(params) {
    try {
      // const res = await this.ctx.model.MillConstruction.updateOne({ _id: params.millId }, { $set: { children: params.workspaces } });
      const res = await this.ctx.service.db.updateOne(
        'MillConstruction',
        { _id: params.millId },
        { $set: { children: params.workspaces } }
      );
      if (res.nModified > 0) {
        return '更新成功';
      }
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async dropUpdateMill(params) {
    try {
      const { ctx } = this;
      const millIds = params.mills.map(item => item._id);
      console.log(millIds, 'millIds');
      await ctx.service.db.deleteMany('MillConstruction', {
        _id: { $in: millIds },
      });
      const mills = this.processEmployees(params.mills).map((mill, index) => ({
        ...mill,
        sortIndex: index,
      }));
      await ctx.service.db.insertMany('MillConstruction', mills);
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  /**
   * 递归处理employees属性
   * @param {Array} data - 需要处理的数据
   * @return {Array} 处理后的数据
   */
  processEmployees(data) {
    if (!data || !Array.isArray(data)) {
      return data;
    }

    return data.map(item => {
      // 复制对象，避免修改原始数据
      const newItem = { ...item };

      // 处理 employees 对象，确保员工数据有正确的 ID
      if (newItem.employees && typeof newItem.employees === 'object') {
        // 如果 employees 是单个对象而非数组
        if (!newItem.employees._id) {
          // 如果没有_id，创建一个新的唯一id
          newItem.employees = newItem.employees._id;
        }
      }

      // 递归处理子节点
      if (newItem.children && Array.isArray(newItem.children)) {
        newItem.children = this.processEmployees(newItem.children);
      }

      return newItem;
    });
  }


  async copyWorkspace(params) {
    try {
      // await this.ctx.model.MillConstruction.updateOne({ _id: params.millId }, { $push: { children: params.workspace } });
      await this.ctx.service.db.updateOne(
        'MillConstruction',
        { _id: params.millId },
        { $push: { children: params.workspace } }
      );
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async copyStation(params) {
    try {
      // await this.ctx.model.MillConstruction.updateOne({ _id: params.millId, 'children._id': params.workspaceId }, { $push: { 'children.$.children': params.workspace } });
      await this.ctx.service.db.updateOne(
        'MillConstruction',
        { _id: params.millId, 'children._id': params.workspaceId },
        { $push: { 'children.$.children': params.workspace } }
      );
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async copyEmployee(params) {
    try {
      const { ctx } = this;
      let res = {};
      let employeeInfo = {};
      if (params.millId) {
        const pipeline = [
          { $match: { _id: params.millId } },
          { $unwind: '$children' },
          { $match: { 'children._id': params.workspaceId } },
          { $unwind: '$children.children' },
          { $match: { 'children.children._id': params.stationId } },
          {
            $match: {
              'children.children.children.employees': params.employeeId,
            },
          },
        ];
        employeeInfo = await ctx.service.db.aggregate(
          'MillConstruction',
          pipeline
        );
        if (employeeInfo.length > 0) {
          return { errMsg: '该岗位已存在该人员，请勿重复添加' };
        }
        // res = await ctx.model.MillConstruction.updateOne({ _id: params.millId }, {
        // $addToSet: {
        //     'children.$[i].children.$[j].children': { employees: params.employeeId },
        //   },
        // }, {
        //   arrayFilters: [{
        //     'i._id': params.workspaceId,
        //   }, { 'j._id': params.stationId },
        //   ],
        // });
        res = await ctx.service.db.updateOne(
          'MillConstruction',
          { _id: params.millId },
          {
            $addToSet: {
              'children.$[i].children.$[j].children': {
                employees: params.employeeId,
              },
            },
          },
          {
            arrayFilters: [
              {
                'i._id': params.workspaceId,
              },
              { 'j._id': params.stationId },
            ],
          }
        );
      } else {
        const pipeline = [
          { $match: { _id: params.workspaceId } },
          { $unwind: '$children' },
          { $match: { 'children._id': params.stationId } },
          { $unwind: '$children.children' },
          { $match: { 'children.children.employees': params.employeeId } },
        ];
        employeeInfo = await ctx.service.db.aggregate(
          'MillConstruction',
          pipeline
        );
        if (employeeInfo.length > 0) {
          return { errMsg: '该岗位已存在该人员，请勿重复添加' };
        }
        // res = await ctx.model.MillConstruction.updateOne({ _id: params.workspaceId, 'children._id': params.stationId }, {
        //   $addToSet: {
        //     'children.$.children': { employees: params.employeeId },
        //   },
        // });
        res = await ctx.service.db.updateOne(
          'MillConstruction',
          { _id: params.workspaceId, 'children._id': params.stationId },
          {
            $addToSet: {
              'children.$.children': { employees: params.employeeId },
            },
          }
        );
      }
      if (res.nModified > 0) {
        const EnterpriseID = ctx.session.adminUserInfo
          ? ctx.session.adminUserInfo.EnterpriseID
          : '';
        // 更新员工工作变更状态
        const statusChange = {
          changType: 2,
          EnterpriseID,
          stationsTo: [ params.stationId ],
          message: '转岗',
        };
        // await ctx.model.EmployeeStatusChange.updateOne({ employee: params.employeeId }, { $addToSet: { statusChanges: statusChange } });
        await ctx.service.db.updateOne(
          'EmployeeStatusChange',
          { employee: params.employeeId },
          { $addToSet: { statusChanges: statusChange } }
        );
        return '添加成功';
      }
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async findAllHarmFactors() {
    try {
      const pipeline = [
        { $unwind: '$chineseName' },
        {
          $group: {
            _id: '$catetory',
            harmFactors: { $push: { label: '$chineseName' } },
          },
        },
        { $project: { label: '$_id', harmFactors: 1 } },
      ];
      const res = await this.ctx.service.db.aggregate(
        'OccupationalexposureLimits',
        pipeline
      );
      return res;
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async moveEmployees(params) {
    console.log(params, 'params');
    try {
      const { ctx } = this;
      let info = {};
      let employeeObjs = [];
      for (let i = 0; i < params.employeeId.length; i++) {
        // info = await ctx.model.MillConstruction.findOne({ $or: [{ 'children.children.employees': params.employeeId[i] }, { 'children.children.children.employees': params.employeeId[i] }] });
        if (params.selectStationIds.length === 3) {
          const pipeline = [
            { $match: { _id: params.selectStationIds[0] } },
            { $unwind: '$children' },
            { $match: { 'children._id': params.selectStationIds[1] } },
            { $unwind: '$children.children' },
            { $match: { 'children.children._id': params.selectStationIds[2] } },
            {
              $match: {
                'children.children.children.employees': params.employeeId[i],
              },
            },
          ];
          info = await ctx.service.db.aggregate('MillConstruction', pipeline);
        } else {
          const pipeline = [
            { $match: { _id: params.selectStationIds[0] } },
            { $unwind: '$children' },
            { $match: { 'children._id': params.selectStationIds[1] } },
            { $unwind: '$children.children' },
            { $match: { 'children.children.employees': params.employeeId[i] } },
          ];
          info = await ctx.service.db.aggregate('MillConstruction', pipeline);
        }
        console.log(info, 'info-----------');
        if (info.length > 0) {
          return { errMsg: '该岗位已存在该人员，请勿重复添加' };
        }
        employeeObjs.push({ employees: params.employeeId[i] });
      }

      // 先移除
      await this.deleteEmployee(params, true);
      // 后添加
      employeeObjs = params.employeeId.map(item => {
        item = { employees: item, isPass: '1' }; // ==>jhw
        return item;
      });

      let res = {};
      if (params.selectStationIds.length === 3) {
        // res = await ctx.model.MillConstruction.updateOne({ _id: params.selectStationIds[0] }, { $addToSet: { 'children.$[i].children.$[j].children': { $each: employeeObjs } } },
        //   {
        //     arrayFilters: [
        //       { 'i._id': params.selectStationIds[1] },
        //       { 'j._id': params.selectStationIds[2] },
        //     ],
        //   });
        res = await ctx.service.db.updateOne(
          'MillConstruction',
          { _id: params.selectStationIds[0] },
          {
            $addToSet: {
              'children.$[i].children.$[j].children': { $each: employeeObjs },
            },
          },
          {
            arrayFilters: [
              { 'i._id': params.selectStationIds[1] },
              { 'j._id': params.selectStationIds[2] },
            ],
          }
        );
      } else {
        // res = await ctx.model.MillConstruction.updateOne({ _id: params.selectStationIds[0] }, { $addToSet: { 'children.$[i].children': { $each: employeeObjs } } },
        //   {
        //     arrayFilters: [
        //       { 'i._id': params.selectStationIds[1] },
        //     ],
        //   });
        res = await ctx.service.db.updateOne(
          'MillConstruction',
          { _id: params.selectStationIds[0] },
          { $addToSet: { 'children.$[i].children': { $each: employeeObjs } } },
          {
            arrayFilters: [{ 'i._id': params.selectStationIds[1] }],
          }
        );
      }
      let EnterpriseID = ctx.session.adminUserInfo
        ? ctx.session.adminUserInfo.EnterpriseID
        : '';
      EnterpriseID = params.enterpriseId || EnterpriseID;

      // 更新员工工作变更状态
      const statusChange = {
        changType: 2,
        EnterpriseID,
        stationFrom: params.stationId,
        stationsTo: [
          params.selectStationIds[params.selectStationIds.length - 1],
        ],
        changStationReason: params.changStationReason,
        files: params.files,
        timestamp: params.timestamp,
        message: '转岗',
      };
      // await ctx.model.EmployeeStatusChange.updateMany({ employee: { $in: params.employeeId } }, { $addToSet: { statusChanges: statusChange } });
      await ctx.service.db.updateMany(
        'EmployeeStatusChange',
        { employee: { $in: params.employeeId } },
        { $addToSet: { statusChanges: statusChange } }
      );

      // 更新员工转岗是否已读 ===> jhw
      for (let index = 0; index < employeeObjs.length; index++) {
        const obj = {};
        const element = employeeObjs[index];
        const people = await ctx.service.db.find('Employee', {
          _id: element.employees,
        });
        obj.employeeId = people[0]._id;
        const userInfo = await ctx.service.db.find('User', {
          companyId: { $in: [ EnterpriseID ] },
          idNo: people[0].IDNum,
        });
        if (userInfo.length > 0) {
          const WorkChanges =
            await ctx.service.employee.findEmployeeWorkChanges({
              employee: people[0]._id,
            });
          obj.beforePosition =
            WorkChanges[0].statusChanges[
              WorkChanges[0].statusChanges.length - 1
            ].stationFrom;
          obj.nowPosition =
            WorkChanges[0].statusChanges[
              WorkChanges[0].statusChanges.length - 1
            ].stationsTo[0];
          obj.isRead = '1';
          // const res = await ctx.model.User.findOneAndUpdate({ _id: userInfo[0]._id }, { $set: { aboutTransfer: obj } }, { new: true });
          const res = await ctx.service.db.findOneAndUpdate(
            'User',
            { _id: userInfo[0]._id },
            { $set: { aboutTransfer: obj } },
            { new: true }
          );
          console.log(res);
        } else {
          continue;
        }
      }
      if (res.nModified > 0) {
        // await this.getStatistics(EnterpriseID);
        return '删除成功';
      }
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async getCheckResult(params) {
    return await this.getStationsCheckResult(params);
  }

  async findEmployeeById(params) {
    try {
      const { ctx } = this;
      const EnterpriseID = params.EnterpriseID;
      const employeeInfo = (await ctx.service.employee.findAllEmployees(params))
        .res[0];
      // 查询所在岗位及危害因素
      const stations = await ctx.service.zygrRecord.findEmployeeStation({
        EnterpriseID,
        employeeId: params._id,
      });
      const checkResult = await this.getStationsCheckResult(stations);
      const enterpriseIds = await ctx.helper.getScopeData('enterprise_ids');
      const products = await ctx.service.db.find(
        'ReceiveRecord',
        { EnterpriseID: { $in: enterpriseIds }, employee: params._id },
        'departName products acknowledge receiveDate isRejected',
        { lean: true }
      );
      const employeeDefends = [];
      if (products && Array.isArray(products)) {
        products.forEach(item => {
          if (item.products && Array.isArray(item.products)) {
            item.products.forEach(product => {
              let status = '';
              if (item.receiveDate && item.isRejected === false) {
                status = '已领取';
              } else if (item.receiveDate && item.isRejected === true) {
                status = '已拒绝';
              } else {
                status = '未领取';
              }
              employeeDefends.push({
                departName: item.departName,
                number: product.number,
                name: product.product,
                status,
                productSpec: product.productSpec,
                receiveDate: item.receiveDate
                  ? moment(item.receiveDate).format('YYYY-MM-DD')
                  : '',
              });
            });
          }
        });
      }
      const employeeTrainings = await ctx.service.db.find(
        'PersonalTraining',
        { EnterpriseID: { $in: enterpriseIds }, userId: params._id },
        'completeState duration studyTime validStudyTime actualScore validScore unitTrain',
        { populate: { path: 'unitTrain', select: 'content type' }, lean: true }
      );
      if (employeeTrainings && Array.isArray(employeeTrainings)) {
        employeeTrainings.forEach(item => {
          if (item.unitTrain) {
            item.content = item.unitTrain.content;
            item.type = item.unitTrain.type;
            delete item.unitTrain;
          }
        });
      }
      if (ctx.session.adminUserInfo.isTrialUser) {
        // 试用账号
        employeeInfo.IDNum = employeeInfo.IDNum
          ? employeeInfo.IDNum.replace(/^(\d{6})\d{8}(\d+)/, '$1********$2')
          : '';
        employeeInfo.phoneNum = employeeInfo.phoneNum
          ? employeeInfo.phoneNum.replace(/^(\d{3})\d{4}(\d+)/, '$1****$2')
          : '';
      }
      return {
        employeeInfo,
        stations,
        checkResult,
        employeeDefends,
        employeeTrainings,
      };
    } catch (error) {
      console.log(error);
    }
  }

  // 获取检测结果
  async getStationsCheckResult(stations) {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo
      ? ctx.session.adminUserInfo.EnterpriseID
      : '';
    let checkResultItem = [];
    let model = '';
    let matchOrOne = {};
    // let matchOrTwo = {};
    // let matchAnd = {};
    let project = {};
    const checkResult = {};
    let modelName = '';
    let station = {};
    let checkProjectFields = {};
    for (let i = 0; i < stations.length; i++) {
      station = stations[i];
      station.name = station.name.replace('岗位', '').trim();
      station.workspace = station.workspace.replace('车间', '').trim();
      for (let j = 0; j < station.harmFactorsAndSort.length; j++) {
        checkProjectFields = {};
        if (station.harmFactorsAndSort[j][0] === '化学') {
          model = 'chemistryFactors';
          modelName = '化学';
          checkProjectFields[model + '.formData.checkProject'] =
            station.harmFactorsAndSort[j][1].trim();
        } else if (station.harmFactorsAndSort[j][0] === '粉尘') {
          model = 'dustFactors';
          modelName = '粉尘';
          checkProjectFields[model + '.formData.checkProject'] = {
            $regex: station.harmFactorsAndSort[j][1].trim(),
          };
        } else if (station.harmFactorsAndSort[j][0] === '生物') {
          model = 'biologicalFactors';
          modelName = '生物';
          checkProjectFields[model + '.formData.checkProject'] =
            station.harmFactorsAndSort[j][1].trim();
        } else {
          if (station.harmFactorsAndSort[j][1].indexOf('噪声') !== -1) {
            model = 'noiseFactors';
            modelName = '噪声';
          } else if (station.harmFactorsAndSort[j][1].indexOf('高温') !== -1) {
            model = 'heatFactors';
            modelName = '高温';
          } else if (
            station.harmFactorsAndSort[j][1].indexOf('超高频辐射') !== -1
          ) {
            model = 'ultraHighRadiationFactors';
            modelName = '超高频辐射';
          } else if (
            station.harmFactorsAndSort[j][1].indexOf('高频电磁场') !== -1
          ) {
            model = 'highFrequencyEleFactors';
            modelName = '高频电磁场';
          } else if (
            station.harmFactorsAndSort[j][1].indexOf('工频电磁场') !== -1 ||
            station.harmFactorsAndSort[j][1].indexOf('工频电场') !== -1
          ) {
            model = 'powerFrequencyElectric';
            modelName = '工频电场';
          } else if (station.harmFactorsAndSort[j][1].indexOf('激光') !== -1) {
            model = 'laserFactors';
            modelName = '激光辐射';
          } else if (station.harmFactorsAndSort[j][1].indexOf('微波') !== -1) {
            model = 'microwaveFactors';
            modelName = '微波辐射';
          } else if (
            station.harmFactorsAndSort[j][1].indexOf('紫外线') !== -1 ||
            station.harmFactorsAndSort[j][1].indexOf('紫外辐射') !== -1
          ) {
            model = 'ultravioletFactors';
            modelName = '紫外辐射';
          } else if (station.harmFactorsAndSort[j][1].indexOf('振动') !== -1) {
            model = 'handBorneVibrationFactors';
            modelName = '手传振动';
          } else if (
            station.harmFactorsAndSort[j][1].indexOf('游离二氧化硅') !== -1
          ) {
            model = 'SiO2Factors';
            modelName = '游离二氧化硅';
          }
        }
        if (model) {
          matchOrOne = {};
          // matchOrTwo = {};
          // matchAnd = {};
          project = {};
          matchOrOne[model + '.formData.station'] = { $regex: station.name };
          matchOrOne[model + '.formData.workspace'] = {
            $regex: station.workspace,
          };
          // matchOrTwo[model + '.formData.checkAddress'] = { $regex: station.workspace.indexOf('车间') ? station.workspace.trim() : station.workspace.trim() + '车间' };
          // matchAnd[model + '.formData.checkAddress'] = { $regex: '车间' + station.name.trim() };
          project[model] = 1;
          project.jobHealthId = 1;
          project['jobHealth.name'] = 1;
          project['jobHealth.reportTime'] = 1;
          const pipeline = [
            { $match: { EnterpriseID } },
            {
              $lookup: {
                from: 'jobhealths',
                localField: 'jobHealthId',
                foreignField: '_id',
                as: 'jobHealth',
              },
            },
            { $unwind: '$jobHealth' },
            { $unwind: '$' + model + '.formData' },
            {
              $match: {
                ...checkProjectFields,
                $or: [
                  matchOrOne,
                  {
                    $and: [
                      {
                        [model + '.formData.checkAddress']: {
                          $regex: station.name,
                        },
                      },
                      {
                        [model + '.formData.checkAddress']: {
                          $regex: station.workspace,
                        },
                      },
                    ],
                  },
                ],
              },
            },
            {
              $project: project,
            },
          ];
          checkResultItem = await ctx.service.db.aggregate(
            'CheckAssessment',
            pipeline
          );
          for (let k = 0; k < checkResultItem.length; k++) {
            if (
              checkResultItem[k] &&
              checkResultItem[k].jobHealth &&
              checkResultItem[k][model].formData
            ) {
              checkResultItem[k][model].formData.checkTime = checkResultItem[k]
                .jobHealth.reportTime
                ? moment(
                  new Date(checkResultItem[k].jobHealth.reportTime)
                ).format('YYYY-MM-DD')
                : '';
              checkResultItem[k][model].formData.checkName =
                checkResultItem[k].jobHealth.name;
              checkResultItem[k][model].formData.checkProject =
                checkResultItem[k][model].formData.checkProject || modelName;
              checkResultItem[k][model].formData.checkResult =
                checkResultItem[k][model].formData.checkResult ||
                checkResultItem[k][model].formData.conclusion;
              checkResultItem[k][model].formData.protectiveFacilities =
                station.protectiveFacilities;
              if (Object.keys(checkResult).indexOf('' + model) === -1) {
                checkResult[model] = { data: [] };
              }
              checkResult[model].model = model;
              checkResult[model].modelName = modelName;
              checkResult[model].data.push(checkResultItem[k][model].formData);
            }
          }
        }
      }
    }
    return Object.values(checkResult);
  }

  async findHealthheckResult(params) {
    const { ctx } = this;
    const res = await this.ctx.service.db.find(
      'Suspect',
      { employeeId: params._id },
      {},
      {
        populate: { path: 'batch', select: '_id' },
        sort: { checkDate: -1 },
        lean: true,
      }
    );
    for (let i = 0; i < res.length; i++) {
      const item = res[i];
      if (item.caseCard && item.caseCard.staticName) {
        item.caseCard.url = await ctx.helper.concatenatePath({
          path:
            ctx.app.config.upload_http_path +
            '/' +
            item.EnterpriseID +
            '/' +
            item.caseCard.staticName,
        });
      }
    }
    return res;
  }

  // 获取物质安全数据表
  async getMaterialInfoList(params) {
    const { ctx } = this;
    console.log(params, 'params');

    const regexArray = params.map(param => new RegExp(param));
    console.log(regexArray);
    const pipeline = [
      {
        $match: {
          basis: {
            $in: params,
          },
        },
      },
    ];
    return await ctx.service.db.aggregate('MsdsTable', pipeline);
  }

  // 工作场所关联部门
  async workshopLinkDeparts({ data, stationId, workspaceId, millId }) {
    const { ctx } = this;

    const departsObj = {
      EnterpriseID: data.EnterpriseID,
      departs: [],
    };

    let staffs = []; // 拉取部门人员

    for (let i = 0; i < data.departs.length; i++) {
      const item = data.departs[i];
      // 查询所属EnterpriseID
      const depart = await ctx.service.db.findOne('Dingtree', {
        _id: item.departId,
      });
      if (depart) {
        departsObj.departs.push(item);

        // 获取这个部门下的所有人员
        await this.getDepartStaffs({ _id: item.departId, staffs });
      }
    }

    staffs = Array.from(new Set(staffs)); // 人员去重
    const newStaffs = staffs.map(e => {
      return {
        employees: e,
      };
    }); // 格式化下
    // 由于工作场所格式发生变化，第一级为企业id，所以判断是否是企业id
    const targetEnterpriseID = await ctx.service.db.findOne(
      'Adminorg',
      { _id: millId },
      {},
      { authCheck: false }
    );
    if (targetEnterpriseID) {
      millId = '';
    }

    let updateRes = '';
    if (millId && stationId) {
      updateRes = await ctx.service.db.updateOne(
        'MillConstruction',
        { _id: millId },
        {
          $set: {
            'children.$[i].children.$[j].newLinkDeparts': departsObj,
            'children.$[i].children.$[j].children': newStaffs,
          },
        },
        { arrayFilters: [{ 'i._id': workspaceId }, { 'j._id': stationId }] }
      );
    } else if (workspaceId && stationId) {
      updateRes = await ctx.service.db.updateOne(
        'MillConstruction',
        { _id: workspaceId },
        {
          $set: {
            'children.$[i].newLinkDeparts': departsObj,
            'children.$[i].children': newStaffs,
          },
        },
        { arrayFilters: [{ 'i._id': stationId }] }
      );
    }

    if (updateRes && updateRes.ok) {
      this.generateEmployeeStatusChange(staffs, departsObj, stationId);

      return { type: 'success', message: '绑定成功', data: departsObj };
    }
    return { type: 'warning', message: '绑定失败' };
  }

  async generateEmployeeStatusChange(staffs, departsObj, stationId) {
    const { ctx } = this;
    // 给每个员工生成一条转岗记录
    for (let i = 0; i < staffs.length; i++) {
      const staff = staffs[i];

      await ctx.service.db.updateMany(
        'EmployeeStatusChange',
        { employee: staff },
        {
          $addToSet: {
            statusChanges: {
              changType: 2,
              EnterpriseID: departsObj.EnterpriseID,
              timestamp: new Date(),
              stationsTo: [ stationId ],
              message: '转岗',
            },
          },
        }
      );

      // 查询是否存在一人一档，若不存在，则生成一份出来
      try {
        const isZygrRecord = await ctx.service.db.findOne('ZygrRecord', {
          employeeId: staff,
        });
        if (!isZygrRecord) {
          await ctx.service.zygrRecord.generateMany({ employeeId: staff });
        }
      } catch (err) {
        console.log(3123, err);
        // throw err;
        continue;
      }
    }
  }

  // 查询目标岗位
  async findTargetStation(stationId, workspaceId, millId) {
    console.log(33333, stationId, workspaceId, millId);
    const { ctx } = this;

    if (millId && stationId) {
      const pipeline = [
        {
          $match: { _id: millId },
        },
        {
          $unwind: '$children',
        },
        {
          $match: { 'children._id': workspaceId },
        },
        {
          $unwind: '$children.children',
        },
        {
          $match: { 'children.children._id': stationId },
        },
        {
          $replaceRoot: {
            newRoot: '$children.children',
          },
        },
      ];
      const doc = await ctx.service.db.aggregate('MillConstruction', pipeline);

      return doc[0];
    } else if (workspaceId && stationId) {
      const pipeline = [
        {
          $match: { _id: workspaceId },
        },
        {
          $unwind: '$children',
        },
        {
          $match: { 'children._id': stationId },
        },
        {
          $replaceRoot: {
            newRoot: '$children',
          },
        },
      ];
      const doc = await ctx.service.db.aggregate('MillConstruction', pipeline);

      return doc[0];
    }
  }

  // 获取部门下的所有人员
  async getDepartStaffs({ _id, staffs }) {
    const { ctx } = this;

    // 查询该部门下的所有员工
    const employees = await ctx.service.db.find('Employee', {
      departs: _id,
      enable: true,
      status: 1, // 在岗状态
    });

    if (employees && employees.length > 0) {
      // 提取员工ID并添加到staffs数组
      const employeeIds = employees.map(emp => emp._id);
      staffs.push(...employeeIds);
    }

    // 递归查询子部门
    const childrenDeparts = await ctx.service.db.find('Dingtree', {
      parentid: _id,
    });

    if (childrenDeparts && childrenDeparts.length > 0) {
      for (let i = 0; i < childrenDeparts.length; i++) {
        const depart = childrenDeparts[i];
        await this.getDepartStaffs({ _id: depart._id, staffs });
      }
    }
  }

  async updateOldLinkDepartsToNew() {
    const { ctx } = this;
    const pipeline = [
      {
        $match: {
          category: 'mill',
          'children.children.linkDeparts.EnterpriseID': { $exists: true },
        },
      },
      {
        $unwind: '$children',
      },
      {
        $unwind: '$children.children',
      },
      {
        $match: {
          'children.children.linkDeparts.EnterpriseID': { $exists: true },
          'children.children.newLinkDeparts.EnterpriseID': { $exists: false },
        },
      },
    ];
    const mills = await ctx.service.db.aggregate('MillConstruction', pipeline);
    const pipelineDing = [
      {
        $project: {
          _id: 1,
          name: 1,
          shortName: 1,
        },
      },
    ];
    const dingtrees = await ctx.service.db.aggregate('Dingtree', pipelineDing);

    for (let i = 0; i < mills.length; i++) {
      const mill = mills[i];
      const oldLinkDeparts = mill.children.children.linkDeparts;
      const newLinkDeparts = {
        EnterpriseID: oldLinkDeparts.EnterpriseID,
        departs: [],
      };

      for (let j = 0; j < oldLinkDeparts.departs.length; j++) {
        const departsOldArr = oldLinkDeparts.departs[j];
        const departArr = departsOldArr.map(e => {
          const target = dingtrees.find(d => d._id === e);
          if (target) {
            return target.shortName ? target.shortName : target.name;
          }
          return '';
        });
        const newDepart = {
          label: departArr.join('/'),
          departId: departsOldArr[departsOldArr.length - 1],
        };
        newLinkDeparts.departs.push(newDepart);
      }
      // await ctx.model.MillConstruction.updateOne(
      await ctx.service.db.updateOne(
        'MillConstruction',
        { _id: mill._id },
        {
          $set: {
            'children.$[i].children.$[j].newLinkDeparts': newLinkDeparts,
          },
        },
        {
          arrayFilters: [
            { 'i._id': mill.children._id },
            { 'j._id': mill.children.children._id },
          ],
        }
      );
    }
    const pipelineWork = [
      {
        $match: {
          category: 'workspaces',
          'children.linkDeparts.EnterpriseID': { $exists: true },
        },
      },
      {
        $unwind: '$children',
      },
      {
        $match: {
          'children.linkDeparts.EnterpriseID': { $exists: true },
          'children.newLinkDeparts.EnterpriseID': { $exists: false },
        },
      },
    ];
    const workspaces = await ctx.service.db.aggregate(
      'MillConstruction',
      pipelineWork
    );

    for (let i = 0; i < workspaces.length; i++) {
      const workspace = workspaces[i];

      const oldLinkDeparts = workspace.children.linkDeparts;
      const newLinkDeparts = {
        EnterpriseID: oldLinkDeparts.EnterpriseID,
        departs: [],
      };

      for (let j = 0; j < oldLinkDeparts.departs.length; j++) {
        const departsOldArr = oldLinkDeparts.departs[j];
        const departArr = departsOldArr.map(e => {
          const target = dingtrees.find(d => d._id === e);
          if (target) {
            return target.shortName ? target.shortName : target.name;
          }
          return '';
        });
        const newDepart = {
          label: departArr.join('/'),
          departId: departsOldArr[departsOldArr.length - 1],
        };
        newLinkDeparts.departs.push(newDepart);
      }

      // await ctx.model.MillConstruction.updateOne(
      await ctx.service.db.updateOne(
        'MillConstruction',
        { _id: workspace._id },
        { $set: { 'children.$[i].newLinkDeparts': newLinkDeparts } },
        { arrayFilters: [{ 'i._id': workspace.children._id }] }
      );
    }
  }
  async updateWrokPlaceLinkDeparts() {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo
      ? ctx.session.adminUserInfo.EnterpriseID
      : '';

    const mills = await ctx.service.db.find('MillConstruction', {
      EnterpriseID,
    });
    if (!mills) {
      return { type: 'success', message: '暂无更新的绑定' };
    }
    // const res = [];
    for (let i = 0; i < mills.length; i++) {
      const mill = mills[i];
      if (mill.category === 'mill') {
        const workspaces = mill.children;
        for (let j = 0; j < workspaces.length; j++) {
          const workspace = workspaces[j];
          for (let z = 0; z < workspace.children.length; z++) {
            const item = workspace.children[z];

            if (item.newLinkDeparts && item.newLinkDeparts.departs) {
              const linkDeparts = item.newLinkDeparts.departs.map(
                e => e.departId
              );
              let staffs = [];

              for (let e = 0; e < linkDeparts.length; e++) {
                const departId = linkDeparts[e];
                await this.getDepartStaffs({ _id: departId, staffs });
              }

              staffs = Array.from(new Set(staffs)); // 人员去重
              const newStaffs = staffs.map(e => {
                return {
                  employees: e,
                };
              }); // 格式化下

              const updateRes = await ctx.service.db.updateOne(
                'MillConstruction',
                { _id: mill._id },
                {
                  $set: {
                    'children.$[i].children.$[j].children': newStaffs,
                  },
                },
                {
                  arrayFilters: [
                    { 'i._id': workspace._id },
                    { 'j._id': item._id },
                  ],
                }
              );

              console.log(111, updateRes);
            }
          }
        }
      } else if (mill.category === 'workspaces') {
        const children = mill.children;
        for (let j = 0; j < children.length; j++) {
          const item = children[j];
          if (item.newLinkDeparts && item.newLinkDeparts.departs) {
            const linkDeparts = item.newLinkDeparts.departs.map(
              e => e.departId
            );
            let staffs = [];

            for (let e = 0; e < linkDeparts.length; e++) {
              const departId = linkDeparts[e];
              await this.getDepartStaffs({ _id: departId, staffs });
            }

            staffs = Array.from(new Set(staffs)); // 人员去重
            const newStaffs = staffs.map(e => {
              return {
                employees: e,
              };
            }); // 格式化下

            const updateRes = await ctx.service.db.updateOne(
              'MillConstruction',
              { _id: mill._id },
              {
                $set: {
                  'children.$[i].children': newStaffs,
                },
              },
              { arrayFilters: [{ 'i._id': item._id }] }
            );

            console.log(222, updateRes);
          }
        }
      }
    }
    return { type: 'success', message: '同步完成' };
  }

  async getByReviewList(query) {
    const { ctx } = this;
    const {
      pageSize = 10,
      currentPage = 1,
      searchValue = '',
      status,
      lastValue = '',
    } = query;
    // const enterpriseIds = await ctx.service.employee.findSubCompany(
    //   EnterpriseID
    // );
    const enterpriseIds = await ctx.helper.getScopeData('enterprise_ids');
    const skip =
      searchValue && searchValue !== lastValue
        ? 0
        : (Number(currentPage) - 1) * Number(pageSize);
    const limit = Number(pageSize);
    const matchQuery = {
      nowEnterpriseID: {
        $in: enterpriseIds,
      },
    };
    if (searchValue.trim() !== '') {
      matchQuery.employeeName = { $regex: searchValue, $options: 'i' };
    }
    if (status) {
      matchQuery.status = Number(status);
    } else {
      matchQuery.status = {
        $ne: 3,
      };
    }
    const pipeline = [
      {
        $match: matchQuery,
      },
      {
        $graphLookup: {
          from: 'dingtrees',
          startWith: '$nowDingtreeIds',
          connectFromField: 'parentid',
          connectToField: '_id',
          as: 'nowDingtreeHierarchy',
        },
      },
      {
        $graphLookup: {
          from: 'dingtrees',
          startWith: '$originDingtreeIds',
          connectFromField: 'parentid',
          connectToField: '_id',
          as: 'originDingtreeHierarchy',
        },
      },
      {
        $addFields: {
          nowDingtreeNames: {
            $map: {
              input: '$nowDingtreeHierarchy',
              as: 'tree',
              in: '$$tree.name',
            },
          },
          originDingtreeNames: {
            $map: {
              input: '$originDingtreeHierarchy',
              as: 'tree',
              in: '$$tree.name',
            },
          },
        },
      },
      {
        $lookup: {
          from: 'employees',
          localField: 'employeeId',
          foreignField: '_id',
          as: 'employeeInfo',
        },
      },
      {
        $lookup: {
          from: 'adminorgs',
          localField: 'nowEnterpriseID',
          foreignField: '_id',
          as: 'nowAdminorgInfo',
        },
      },
      {
        $lookup: {
          from: 'adminorgs',
          localField: 'originEnterpriseID',
          foreignField: '_id',
          as: 'originAdminorgInfo',
        },
      },
      {
        $addFields: {
          employeeName: { $arrayElemAt: [ '$employeeInfo.name', 0 ] },
          employeeStatus: { $arrayElemAt: [ '$employeeInfo.status', 0 ] },
          originCname: {
            $ifNull: [
              { $arrayElemAt: [ '$originAdminorgInfo.shortName', 0 ] },
              { $arrayElemAt: [ '$originAdminorgInfo.cname', 0 ] },
            ],
          },
          nowCname: {
            $ifNull: [
              { $arrayElemAt: [ '$nowAdminorgInfo.shortName', 0 ] },
              { $arrayElemAt: [ '$nowAdminorgInfo.cname', 0 ] },
            ],
          },
          phoneNum: { $arrayElemAt: [ '$employeeInfo.phoneNum', 0 ] },
        },
      },
      {
        $project: {
          employeeInfo: 0,
          nowAdminorgInfo: 0,
          originAdminorgInfo: 0,
        },
      },
      {
        $sort: {
          status: 1,
          createdAt: -1,
        },
      },
      {
        $facet: {
          result: [{ $skip: skip }, { $limit: limit }],
          totalCount: [{ $count: 'count' }],
        },
      },
    ];
    const res = await ctx.service.db.aggregate('AuditStationChange', pipeline);
    const data = res[0].result;
    if (data.length > 0) {
      const processStationIds = async stationIds => {
        if (stationIds.length === 0) return;
        for (let i = 0; i < stationIds.length; i++) {
          const ele = stationIds[i];
          if (ele.length > 0) {
            let stationName = '';
            stationName = await this.findTargetStationInfo(ele);
            ele.push(stationName);
          }
        }
      };

      for (let i = 0; i < data.length; i++) {
        if (data[i].fixedInfo) continue;
        const nowStationIds = data[i].nowStationIds || []; // 如果不存在，就使用一个空数组代替
        const originStationIds = data[i].originStationIds || []; // 如果不存在，就使用一个空数组代替
        await Promise.all([
          processStationIds(nowStationIds),
          processStationIds(originStationIds),
        ]);
      }
    }
    const list = {
      data,
      pageInfo: {
        pageSize,
        currentPage: searchValue && searchValue !== lastValue ? 1 : currentPage,
        totalCount: res[0].totalCount[0] ? res[0].totalCount[0].count : 0,
      },
    };
    return list;
  }

  async findTargetStationInfo(ele) {
    const { ctx } = this;

    let millId = '';
    let workspaceId = '';
    let stationId = '';

    for (const item of ele) {
      if (item.category === 'mill') {
        millId = item.specificId;
      } else if (item.category === 'workspaces') {
        workspaceId = item.specificId;
      } else if (item.category === 'stations') {
        stationId = item.specificId;
      }
    }

    if (millId) {
      const pipeline = [
        { $match: { _id: millId } },
        { $unwind: '$children' },
        { $match: { 'children._id': workspaceId } },
        { $unwind: '$children.children' },
        { $match: { 'children.children._id': stationId } },
        {
          $project: {
            fullName: {
              $concat: [
                '$name',
                '-',
                '$children.name',
                '-',
                '$children.children.name',
              ],
            },
            harmFactors: '$children.children.harmFactors',
            _id: 0,
          },
        },
      ];
      const doc = await ctx.service.db.aggregate('MillConstruction', pipeline, {
        authCheck: false,
      });
      return (doc.length > 0 && doc[0]) || '';
    }

    const pipeline = [
      { $match: { _id: workspaceId } },
      { $unwind: '$children' },
      { $match: { 'children._id': stationId } },
      {
        $project: {
          fullName: {
            $concat: [ '$name', '-', '$children.name' ],
          },
          harmFactors: '$children.harmFactors',
          _id: 0,
        },
      },
    ];
    const doc = await ctx.service.db.aggregate('MillConstruction', pipeline, {
      authCheck: false,
    });
    return (doc.length > 0 && doc[0]) || '';
  }

  async reviewByEmployee(params) {
    const { status, row } = params;
    const {
      employeeId,
      stationStatus,
      nowEnterpriseID,
      originStationIds = [],
      nowStationIds,
      _id,
      nowCname = '',
      originCname = '',
      nowStationName = '',
      nowWorkspaceName = '',
      originStationName = '',
      originWorkspaceName = '',
      nowHarmFactors = '',
      originHarmFactors = '',
      shouldCheck,
      // files,
    } = row;
    const auditTime = new Date();
    const auditUserId = this.ctx.session.adminUserInfo._id;

    try {
      const fixedInfo = {
        nowCname,
        originCname,
        nowStationName,
        nowWorkspaceName,
        originStationName,
        originWorkspaceName,
        nowHarmFactors,
        originHarmFactors,
        shouldCheck,
      };
      if (status !== 3) {
        await this.ctx.service.db.updateOne(
          'AuditStationChange',
          { _id },
          {
            $set: { status, auditTime, auditUserId, fixedInfo },
          }
        );
        if (status === 1) {
          const statusChange = {
            _id: shortid.generate(),
            changType: stationStatus === 4 ? 2 : stationStatus,
            EnterpriseID: nowEnterpriseID,
            stationFrom: this.getStationIdsFrom(originStationIds),
            stationsTo: [ this.getStationIdsFrom(nowStationIds) ],
            changStationReason: this.getStationStatus(stationStatus),
            // files: [ files ],
            timestamp: auditTime,
            message: this.getStationStatus(stationStatus),
          };
          await this.ctx.service.db.updateOne(
            'EmployeeStatusChange',
            { employee: employeeId },
            { $addToSet: { statusChanges: statusChange } }
          );
          const res = await this.ctx.service.db.updateOne(
            'AuditStationChange',
            { _id },
            { $set: { workInfoId: statusChange._id } },
            { new: true }
          );
          if (
            stationStatus === 2 ||
            stationStatus === 4 ||
            stationStatus === 1
          ) {
            if (nowStationIds && nowStationIds[0].length === 4) {
              const millId = nowStationIds[0][0].specificId;
              const workspaceId = nowStationIds[0][1].specificId;
              const stationId = nowStationIds[0][2].specificId;
              const doc = await this.ctx.service.db.findOne(
                'MillConstruction',
                { _id: millId },
                {},
                { lean: true }
              );

              const workspace = doc.children.find(
                child => child._id === workspaceId
              );
              const station = workspace.children.find(
                child => child._id === stationId
              );
              if (originStationIds.length > 0) {
                if (originStationIds[0].length === 4) {
                  const millId = originStationIds[0][0].specificId;
                  const workspaceId = originStationIds[0][1].specificId;
                  const stationId = originStationIds[0][2].specificId;
                  await this.ctx.service.db.updateOne(
                    'MillConstruction',
                    { _id: millId },
                    {
                      $pull: {
                        'children.$[child1].children.$[child2].children': {
                          employees: employeeId,
                        },
                      },
                    },
                    {
                      arrayFilters: [
                        { 'child1._id': workspaceId },
                        { 'child2._id': stationId },
                      ],
                    }
                  );
                } else if (originStationIds[0].length === 3) {
                  const workspaceId = originStationIds[0][0].specificId;
                  const stationId = originStationIds[0][1].specificId;
                  await this.ctx.service.db.updateOne(
                    'MillConstruction',
                    { _id: workspaceId },
                    {
                      $pull: {
                        'children.$[child1].children': {
                          employees: employeeId,
                        },
                      },
                    },
                    {
                      arrayFilters: [{ 'child1._id': stationId }],
                    }
                  );
                }
              }

              if (
                !station.children.some(child =>
                  child.employees.includes(employeeId)
                )
              ) {
                await this.ctx.service.db.updateOne(
                  'MillConstruction',
                  { _id: millId },
                  {
                    $addToSet: {
                      'children.$[i].children.$[j].children': {
                        employees: employeeId,
                      },
                    },
                  },
                  {
                    arrayFilters: [
                      { 'i._id': workspaceId },
                      { 'j._id': stationId },
                    ],
                  }
                );
              }
            } else if (
              nowStationIds &&
              (nowStationIds[0].length === 2 || nowStationIds[0].length === 3)
            ) {
              const workspaceId = nowStationIds[0][0].specificId;
              const stationId = nowStationIds[0][1].specificId;
              const doc = await this.ctx.service.db.findOne(
                'MillConstruction',
                { _id: workspaceId },
                {},
                { lean: true }
              );
              const station = doc.children.find(
                child => child._id === stationId
              );
              if (originStationIds && originStationIds.length > 0) {
                if (originStationIds[0].length === 4) {
                  const millId = originStationIds[0][0].specificId;
                  const workspaceId = originStationIds[0][1].specificId;
                  const stationId = originStationIds[0][2].specificId;
                  await this.ctx.service.db.updateOne(
                    'MillConstruction',
                    { _id: millId },
                    {
                      $pull: {
                        'children.$[child1].children.$[child2].children': {
                          employees: employeeId,
                        },
                      },
                    },
                    {
                      arrayFilters: [
                        { 'child1._id': workspaceId },
                        { 'child2._id': stationId },
                      ],
                    }
                  );
                } else if (originStationIds[0].length === 3) {
                  const workspaceId = originStationIds[0][0].specificId;
                  const stationId = originStationIds[0][1].specificId;
                  await this.ctx.service.db.updateOne(
                    'MillConstruction',
                    { _id: workspaceId },
                    {
                      $pull: {
                        'children.$[child1].children': {
                          employees: employeeId,
                        },
                      },
                    },
                    {
                      arrayFilters: [{ 'child1._id': stationId }],
                    }
                  );
                }
              }
              if (
                !station.children.some(child =>
                  child.employees.includes(employeeId)
                )
              ) {
                await this.ctx.service.db.updateOne(
                  'MillConstruction',
                  { _id: workspaceId },
                  {
                    $addToSet: {
                      'children.$[i].children': {
                        employees: employeeId,
                      },
                    },
                  },
                  {
                    arrayFilters: [{ 'i._id': stationId }],
                  }
                );
              }
            }
            // 查询是否存在一人一档，若不存在，则生成一份出来
            try {
              // 查询zygrRecord表以获取对应的文档
              const zygrRecordQuery = {
                employeeId,
              };
              const zygrRecordDoc = await this.ctx.service.db.findOne(
                'ZygrRecord',
                zygrRecordQuery
              );
              if (!zygrRecordDoc) {
                // 查询是否存在一人一档，若不存在，则生成一份出来
                await this.ctx.service.zygrRecord.generateMany({
                  employeeId: {
                    _id: employeeId,
                  },
                });
                if (res.files && res.files.originName) {
                  const files = res.files;
                  const fileExtension = files.originName.split('.').pop();
                  const nowZygrRecordDoc = await this.ctx.service.db.findOne(
                    'ZygrRecord',
                    zygrRecordQuery
                  );
                  const newClientUpload = {
                    fileType: `.${fileExtension}`,
                    originName: files.originName,
                    staticName: files.staticName,
                    sort: '用户上传',
                  };
                  nowZygrRecordDoc.recordFiles[5].clientUpload.push(
                    newClientUpload
                  );
                  await nowZygrRecordDoc.save();
                }
              } else {
                if (res.files && res.files.originName) {
                  const files = res.files;
                  const fileExtension = files.originName.split('.').pop();
                  const newClientUpload = {
                    fileType: `.${fileExtension}`,
                    originName: files.originName,
                    staticName: files.staticName,
                    sort: '用户上传',
                  };
                  zygrRecordDoc.recordFiles[5].clientUpload.push(
                    newClientUpload
                  );
                  await zygrRecordDoc.save();
                }
              }
            } catch (err) {
              this.ctx.auditLog(
                '创建档案失败',
                `用户${auditUserId}创建${employeeId}档案失败。`,
                'error'
              );
            }
          }
        }
      } else {
        await this.ctx.service.db.updateOne(
          'AuditStationChange',
          { _id },
          { $set: { status, auditTime, auditUserId, fixedInfo } }
        );
      }
      return status;
    } catch (error) {
      console.log(111, error);
      return 0;
    }
  }

  async uploadByEmployeeFile(params) {
    const { ctx } = this;
    const { files, _id, employeeId, fileExtension } = params;
    const res = await ctx.service.db.updateOne(
      'AuditStationChange',
      { _id },
      { $set: { files } }
    );
    // 查询zygrRecord表以获取对应的文档
    const zygrRecordQuery = {
      employeeId,
    };
    const zygrRecordDoc = await this.ctx.service.db.findOne(
      'ZygrRecord',
      zygrRecordQuery
    );
    if (!zygrRecordDoc) {
      // 查询是否存在一人一档，若不存在，则生成一份出来
      await this.ctx.service.zygrRecord.generateMany({
        employeeId: {
          _id: employeeId,
        },
      });
      const nowZygrRecordDoc = await this.ctx.service.db.findOne(
        'ZygrRecord',
        zygrRecordQuery
      );
      const newClientUpload = {
        fileType: `.${fileExtension}`,
        originName: files.originName,
        staticName: files.staticName,
        sort: '用户上传',
      };
      nowZygrRecordDoc.recordFiles[6].clientUpload.push(newClientUpload);
      await nowZygrRecordDoc.save();
    } else {
      const newClientUpload = {
        fileType: `.${fileExtension}`,
        originName: files.originName,
        staticName: files.staticName,
        sort: '用户上传',
      };
      zygrRecordDoc.recordFiles[6].clientUpload.push(newClientUpload);
      await zygrRecordDoc.save();
    }
    return res;
  }

  async getByEmployeeNum() {
    const { ctx } = this;
    // const enterpriseIds = await ctx.service.employee.findSubCompany(
    //   EnterpriseID
    // );
    const enterpriseIds = await ctx.helper.getScopeData('enterprise_ids');
    const count = await ctx.service.db.find(
      'AuditStationChange',
      {
        nowEnterpriseID: { $in: enterpriseIds },
        status: 0,
      },
      {},
      { count: true }
    );
    return count;
  }
  getStationStatus(stationStatus) {
    switch (stationStatus) {
      case 1:
        return '离岗';
      case 2:
        return '转岗';
      case 4:
        return '新上岗';
      default:
        return '未知状态';
    }
  }

  getStationIdsFrom(stationIds) {
    if (stationIds.length && stationIds[0].length === 3) {
      const stationsIds = stationIds[0][1].specificId;
      return stationsIds;
    } else if (stationIds.length && stationIds[0].length === 4) {
      const stationsIds = stationIds[0][2].specificId;
      return stationsIds;
    }
    return '';
  }

  // 获取岗位信息
  async getStationInfo(stationId, EnterpriseID) {
    const pipeline = [
      {
        $match: {
          EnterpriseID,
        },
      },
      {
        $unwind: '$children',
      },
      {
        $match: {
          $or: [
            { 'children._id': stationId },
            { 'children.children._id': stationId },
          ],
        },
      },
    ];
    const mills = await this.ctx.service.db.aggregate(
      'MillConstruction',
      pipeline
    );
    if (mills.length > 0) {
      let stationName = '';
      if (mills[0].category === 'mill') {
        mills[0].children.children.forEach(item => {
          if (item._id === stationId) {
            stationName = item.name;
          }
        });
        stationId = `${mills[0].name}/${mills[0].children.name}/${stationName}`;
      } else {
        stationId = `${mills[0].name}/${mills[0].children.name}`;
      }
    } else {
      stationId = '';
    }
    return stationId;
  }

  async updateChangeStationInfo(data) {
    const { ctx } = this;
    const { _id, filesInfo = [] } = data;
    const res = await ctx.service.db.findOneAndUpdate('AuditStationChange', { _id }, { $set: { filesInfo } }, { new: true });
    console.log(res, 'resserveice==========>');
    const updatedFilesInfo = filesInfo.map(file => {
      // 不涉及 _id，直接操作其他属性
      const fileType = file.originName ? `.${file.originName.split('.').pop()}` : '';
      return {
        docTypes: file.docTypes, // 显式保留需要的字段
        originName: file.originName, // 显式保留
        staticName: file.staticName, // 显式保留
        sort: '用户上传',
        fileType,
      };
    });

    const { employeeId } = res;

    const zygrRecordQuery = {
      employeeId,
    };
    const zygrRecordDoc = await this.ctx.service.db.findOne(
      'ZygrRecord',
      zygrRecordQuery
    );

    // 去重函数：根据staticName过滤重复文件
    const removeDuplicateFiles = (existingFiles, newFiles) => {
      const existingStaticNames = new Set(existingFiles.map(file => file.staticName));
      return newFiles.filter(file => !existingStaticNames.has(file.staticName));
    };

    if (!zygrRecordDoc) {
      // 查询是否存在一人一档，若不存在，则生成一份出来
      await this.ctx.service.zygrRecord.generateMany({
        employeeId: {
          _id: employeeId,
        },
      });
      const nowZygrRecordDoc = await this.ctx.service.db.findOne(
        'ZygrRecord',
        zygrRecordQuery
      );
      const existingFiles = nowZygrRecordDoc.recordFiles[6].clientUpload || [];
      const newFilesToAdd = removeDuplicateFiles(existingFiles, updatedFilesInfo);
      nowZygrRecordDoc.recordFiles[6].clientUpload = [ ...existingFiles, ...newFilesToAdd ];
      await nowZygrRecordDoc.save();
    } else {
      const existingFiles = zygrRecordDoc.recordFiles[6].clientUpload || [];
      const newFilesToAdd = removeDuplicateFiles(existingFiles, updatedFilesInfo);
      zygrRecordDoc.recordFiles[6].clientUpload = [ ...existingFiles, ...newFilesToAdd ];
      await zygrRecordDoc.save();
    }

    return res;

  }


  async getListByDevice(params) {
    try {
      const { ctx } = this;
      const {
        EnterpriseID,
        department = '',
        device = '',
        process = '',
        workType = '',
        currentPage = 1,
        pageSize = 10,
      } = params;

      let enterpriseIds = [];
      // 获取企业ID范围
      if (EnterpriseID) {
        enterpriseIds = [ EnterpriseID ];
      } else {
        enterpriseIds = await ctx.helper.getScopeData('enterprise_ids') || [];
      }

      // 使用 aggregate 管道处理两种不同的数据结构
      const pipeline = [
        // 第一阶段：基础企业匹配
        {
          $match: {
            EnterpriseID: { $in: enterpriseIds },
          },
        },

        // 第二阶段：关联企业信息
        {
          $lookup: {
            from: 'adminorgs',
            let: { enterpriseId: '$EnterpriseID' },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $eq: [ '$_id', '$$enterpriseId' ],
                  },
                },
              },
              {
                $project: {
                  shortName: 1,
                  cname: 1,
                },
              },
            ],
            as: 'enterpriseInfo',
          },
        },

        // 第三阶段：处理两种数据结构，统一提取站点信息
        {
          $addFields: {
            stations: {
              $reduce: {
                input: '$children',
                initialValue: [],
                in: {
                  $let: {
                    vars: {
                      firstLevel: '$$this',
                    },
                    in: {
                      $concatArrays: [
                        '$$value',
                        {
                          $cond: {
                            if: { $eq: [ '$category', 'workspaces' ] },
                            then: {
                              // 结构一处理（两级）
                              $map: {
                                input: {
                                  $filter: {
                                    input: { $ifNull: [ '$children', []] },
                                    cond: { $eq: [ '$$this.category', 'stations' ] },
                                  },
                                },
                                as: 'station',
                                in: {
                                  _id: '$$station._id',
                                  enterpriseName: {
                                    $ifNull: [
                                      { $arrayElemAt: [ '$enterpriseInfo.shortName', 0 ] },
                                      { $arrayElemAt: [ '$enterpriseInfo.cname', 0 ] },
                                    ],
                                  },
                                  department: '', // 一级：无 department 层
                                  device: '$name', // 当前为 workspaces 层
                                  process: '$$station.name', // 岗位名
                                  workType: { $ifNull: [ '$$station.workType', '' ] },
                                  workWay: { $ifNull: [ '$$station.workWay', '' ] },
                                  workTimeDay: { $toString: { $ifNull: [ '$$station.workTimeDay', 0 ] } },
                                  workDayWeek: { $toString: { $ifNull: [ '$$station.workDayWeek', 0 ] } },
                                  protectiveEquipment: { $ifNull: [ '$$station.protectiveEquipment', '无' ] },
                                  protectiveFacilities: { $ifNull: [ '$$station.protectiveFacilities', '无' ] },
                                  harmFactors: { $ifNull: [ '$$station.harmFactors', []] },
                                  employees: { $ifNull: [ '$$station.children', []] },
                                  dataStructure: 'two-level',
                                },
                              },
                            },
                            else: {
                              // 结构二处理（三级）
                              $reduce: {
                                input: { $ifNull: [ '$children', []] },
                                initialValue: [],
                                in: {
                                  $let: {
                                    vars: {
                                      secondLevel: '$$this',
                                    },
                                    in: {
                                      $concatArrays: [
                                        '$$value',
                                        {
                                          $map: {
                                            input: {
                                              $filter: {
                                                input: { $ifNull: [ '$$secondLevel.children', []] },
                                                cond: { $eq: [ '$$this.category', 'stations' ] },
                                              },
                                            },
                                            as: 'station',
                                            in: {
                                              _id: '$$station._id',
                                              enterpriseName: {
                                                $ifNull: [
                                                  { $arrayElemAt: [ '$enterpriseInfo.shortName', 0 ] },
                                                  { $arrayElemAt: [ '$enterpriseInfo.cname', 0 ] },
                                                ],
                                              },
                                              department: '$name', // 一级（mill）名称
                                              device: '$$secondLevel.name', // 二级 workspaces 名称
                                              process: '$$station.name', // 岗位名称
                                              workType: { $ifNull: [ '$$station.workType', '' ] },
                                              workWay: { $ifNull: [ '$$station.workWay', '' ] },
                                              workTimeDay: { $toString: { $ifNull: [ '$$station.workTimeDay', 0 ] } },
                                              workDayWeek: { $toString: { $ifNull: [ '$$station.workDayWeek', 0 ] } },
                                              protectiveEquipment: { $ifNull: [ '$$station.protectiveEquipment', '无' ] },
                                              protectiveFacilities: { $ifNull: [ '$$station.protectiveFacilities', '无' ] },
                                              harmFactors: { $ifNull: [ '$$station.harmFactors', []] },
                                              employees: { $ifNull: [ '$$station.children', []] },
                                              dataStructure: 'three-level',
                                            },
                                          },
                                        },
                                      ],
                                    },
                                  },
                                },
                              },
                            },
                          },

                        },
                      ],
                    },
                  },
                },
              },
            },
          },
        },

        // 第四阶段：展开站点数组
        {
          $unwind: {
            path: '$stations',
            preserveNullAndEmptyArrays: false,
          },
        },

        // 第五阶段：应用筛选条件
        {
          $match: {
            ...(department && { 'stations.department': { $regex: department.trim(), $options: 'i' } }),
            ...(device && { 'stations.device': { $regex: device.trim(), $options: 'i' } }),
            ...(process && { 'stations.process': { $regex: process.trim(), $options: 'i' } }),
            ...(workType && { 'stations.workType': { $regex: workType.trim(), $options: 'i' } }),
          },
        },

        // 第六阶段：只保留站点数据并添加员工统计
        {
          $replaceRoot: {
            newRoot: {
              $mergeObjects: [
                '$stations',
                {
                  childrenCount: { $size: '$stations.employees' },
                  // 提取员工ID用于关联查询
                  employeeIds: {
                    $map: {
                      input: '$stations.employees',
                      as: 'emp',
                      in: '$$emp.employees',
                    },
                  },
                },
              ],
            },
          },
        },

        // 第七阶段：关联员工信息获取性别统计
        {
          $lookup: {
            from: 'employees',
            localField: 'employeeIds',
            foreignField: '_id',
            as: 'employeeDetails',
          },
        },

        // 第八阶段：添加性别统计
        {
          $addFields: {
            male: {
              $size: {
                $filter: {
                  input: '$employeeDetails',
                  cond: { $eq: [ '$$this.gender', '0' ] },
                },
              },
            },
            female: {
              $size: {
                $filter: {
                  input: '$employeeDetails',
                  cond: { $eq: [ '$$this.gender', '1' ] },
                },
              },
            },
          },
        },

        // 第九阶段：清理临时字段
        {
          $project: {
            employeeIds: 0,
            employeeDetails: 0,
            employees: 0,
          },
        },

        // 第十阶段：分页和计数
        {
          $facet: {
            data: [
              { $skip: (currentPage - 1) * pageSize },
              { $limit: pageSize },
            ],
            total: [
              { $count: 'count' },
            ],
          },
        },
      ];


      const aggregateResult = await ctx.service.db.aggregate('MillConstruction', pipeline);

      const data = aggregateResult.length > 0 ? aggregateResult[0].data : [];
      const total = aggregateResult.length > 0 ? aggregateResult[0].total[0].count : 0;

      // 批量获取危害因素详情
      const result = await this.batchFetchHarmFactors(data);

      return {
        list: result,
        pagination: {
          currentPage: Number(currentPage),
          pageSize: Number(pageSize),
          total,
        },
      };

    } catch (error) {
      console.error('getListByDevice error:', error);
      throw error;
    }
  }

  // 批量获取危害因素详情（优化版）
  async batchFetchHarmFactors(stations) {
    const { ctx } = this;

    // 收集所有需要查询的危害因素名称
    const allFactorNames = new Set();

    if (stations && Array.isArray(stations)) {
      stations.forEach(station => {
        if (station && Array.isArray(station.harmFactors)) {
          station.harmFactors.forEach(item => {
            const factorName = Array.isArray(item) ? item[1] : item;
            if (factorName) {
              allFactorNames.add(factorName);
            }
          });
        }
      });
    }

    // 批量查询所有危害因素详情
    const factorNamesArray = Array.from(allFactorNames);
    const harmFactorInfoMap = new Map();

    if (factorNamesArray.length > 0) {
      console.log('批量查询危害因素，数组长度:', factorNamesArray.length);

      // 使用 aggregate 优化危害因素查询
      const factorPipeline = [
        {
          $match: {
            $or: [
              { showName: { $in: factorNamesArray } },
              { chineseName: { $in: factorNamesArray } },
              { catetory: { $in: factorNamesArray } },
            ],
          },
        },
        {
          $project: {
            showName: 1,
            initial: 1,
            chineseName: 1,
            englishName: 1,
            casNum: 1,
            MAC: 1,
            PC_TWA: 1,
            PC_STEL: 1,
            PE: 1,
            healthEffect: 1,
            highHarm: 1,
            seriousHarm: 1,
            catetory: 1,
          },
        },
      ];

      const harmFactorInfos = await ctx.service.db.aggregate('OccupationalexposureLimits', factorPipeline);

      // 构建快速查找的 Map
      harmFactorInfos.forEach(info => {
        const processedInfo = {
          showName: info.showName || '',
          initial: info.initial || '',
          chineseName: Array.isArray(info.chineseName) ? info.chineseName : [ info.chineseName || '' ],
          englishName: info.englishName || '',
          casNum: info.casNum || '',
          oels: {
            pcMac: info.MAC || '',
            pcTwa: info.PC_TWA || '',
            pcStel: info.PC_STEL || '',
            pe: info.PE || '',
          },
          healthEffect: info.healthEffect || '',
          isHighToxic: info.highHarm === '1' ? '是' : '否',
          isSerious: info.seriousHarm === '1' ? '是' : '否',
        };

        // 用多个可能的名称作为 key
        [ info.showName, info.chineseName, info.catetory ].forEach(name => {
          if (name && factorNamesArray.includes(name)) {
            harmFactorInfoMap.set(name, processedInfo);
          }
        });
      });
    }

    // 为每个站点分配对应的危害因素详情
    const result = stations.map(station => {
      const harmFactors = [];

      if (Array.isArray(station.harmFactors)) {
        station.harmFactors.forEach(item => {
          const factorName = Array.isArray(item) ? item[1] : item;
          if (factorName && harmFactorInfoMap.has(factorName)) {
            harmFactors.push(harmFactorInfoMap.get(factorName));
          } else if (factorName) {
            console.warn(`No matching harm factor found for: ${factorName}`);
          }
        });
      }

      return {
        ...station,
        harmFactors,
      };
    });

    return result;
  }

  /**
   * 同步unitCode与encode关系
   * 从whCodeRelationshipData.json读取数据，更新millConstruction模型中的encode字段
   * @return {Object} 处理结果统计
   */
  async syncWhCodeRelationship() {
    const { ctx } = this;
    const fs = require('fs');
    const path = require('path');

    try {
      // 读取JSON文件
      const jsonPath = path.join(ctx.app.config.baseDir, 'app/public/json/whCodeRelationshipData.json');
      const jsonData = JSON.parse(await fs.promises.readFile(jsonPath, 'utf8'));

      // 创建映射关系，提高查询效率
      const unitCodeToEncodeMap = new Map();
      jsonData.forEach(item => {
        if (item.unitCode && item.encode) {
          unitCodeToEncodeMap.set(item.unitCode, item.encode);
        }
      });

      // 统计信息
      const stats = {
        total: unitCodeToEncodeMap.size,
        updated: 0,
        notFound: 0,
        errors: 0,
      };

      // 批量更新数据
      for (const [ unitCode, encode ] of unitCodeToEncodeMap) {
        try {
          const updateResult = await ctx.service.db.updateMany(
            'MillConstruction',
            { unitCode },
            { $set: { encode } },
            {},
            `同步unitCode ${unitCode} 对应的encode ${encode}`
          );

          if (updateResult && updateResult.modifiedCount > 0) {
            stats.updated += updateResult.modifiedCount;
          } else {
            stats.notFound++;
          }
        } catch (updateError) {
          console.error(`更新unitCode ${unitCode} 失败:`, updateError);
          stats.errors++;
        }
      }

      const message = `同步完成！总共处理 ${stats.total} 条映射关系，成功更新 ${stats.updated} 条记录，未找到匹配 ${stats.notFound} 条，错误 ${stats.errors} 条`;

      return {
        success: true,
        message,
        stats,
      };

    } catch (error) {
      console.error('同步unitCode和encode关系失败:', error);
      return {
        success: false,
        message: `同步失败: ${error.message}`,
        stats: null,
      };
    }
  }

  /**
   * @summary 懒加载获取工厂结构数据
   * @description 参考findEnterpriseLevelInfo实现，基于扁平化视图实现层级懒加载
   * @param {Object} params - 查询参数
   * @param {Boolean} showEmployee - 是否显示员工层级，默认不显示
   * @param {String} params.parentId - 父级ID，不传则查询企业列表
   * @return {Promise<Array>} 工厂结构数据列表
   */
  async findMillConstructionLazy(params, showEmployee) {
    const { ctx } = this;

    try {
      // 添加详细的调试日志
      ctx.logger.info('findMillConstructionLazy 开始执行:', {
        params,
        showEmployee,
        sessionExists: !!ctx.session,
        adminUserInfo: ctx.session && ctx.session.adminUserInfo && ctx.session.adminUserInfo._id,
      });

      // 获取权限范围的企业ID
      const scopeEnterpriseIds = await ctx.helper.getScopeData('enterprise_ids') || [];
      ctx.logger.info('获取到的权限范围企业ID:', { scopeEnterpriseIds });

      if (!params.parentId && (scopeEnterpriseIds.length === 1 || params.EnterpriseID)) {
        params.parentId = params.EnterpriseID || scopeEnterpriseIds[0];
        ctx.logger.info('设置 parentId:', { parentId: params.parentId });
      }
      // 如果没有parentId参数，返回企业列表
      if (!params.parentId) {
        ctx.logger.info('查询企业列表，构建聚合管道...');

        const pipeline = [
          {
            $match: {
              EnterpriseID: { $in: scopeEnterpriseIds },
              state: '1',
            },
          },
        {
          $group: {
            _id: '$EnterpriseID',
          },
        },
        {
          $lookup: {
            from: 'adminorgs',
            localField: '_id',
            foreignField: '_id',
            as: 'enterprise',
          },
        },
        {
          $lookup: {
            from: 'flatMillConstructionMaterialized',
            let: { enterpriseId: '$_id' },
            pipeline: [
              {
                $match: {
                  $expr: { $eq: [ '$EnterpriseID', '$$enterpriseId' ] },
                  level: 'mill',
                  state: '1',
                },
              },
              {
                $group: {
                  _id: null,
                  totalCount: { $sum: { $ifNull: [ '$totalEmployeeCount', 0 ] } },
                },
              },
            ],
            as: 'employeeStats',
          },
        },
        {
          $addFields: {
            name: {
              $ifNull: [
                { $arrayElemAt: [ '$enterprise.shortName', 0 ] },
                { $arrayElemAt: [ '$enterprise.cname', 0 ] },
              ],
            },
            category: 'enterprises',
            hasChildren: true,
            totalEmployeeCount: {
              $ifNull: [
                { $arrayElemAt: [ '$employeeStats.totalCount', 0 ] },
                0,
              ],
            },
          },
        },
        {
          $project: {
            enterprise: 0,
            employeeStats: 0,
          },
        },
        {
          $sort: {
            _id: 1,
          },
        },
      ];

      ctx.logger.info('企业列表聚合管道:', JSON.stringify(pipeline, null, 2));

      const result = await ctx.service.db.aggregate('FlatMillConstructionMaterialized', pipeline);
      ctx.logger.info('企业列表查询结果:', { count: result.length });
      return result;
    }

      // 有parentId参数的查询：返回指定父级的子节点
      ctx.logger.info('查询子节点，parentId:', params.parentId);

      const pipeline = [
        {
          $match: {
            parentId: params.parentId,
            state: '1',
          },
        },
      {
        $lookup: {
          from: 'flatMillConstructionMaterialized',
          let: { currentId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: { $eq: [ '$parentId', '$$currentId' ] },
                state: '1',
              },
            },
            { $limit: 1 },
          ],
          as: 'childrenCheck',
        },
      },
      {
        $lookup: {
          from: 'employees',
          localField: 'employees',
          foreignField: '_id',
          pipeline: [
            {
              $match: {
                status: { $ne: 0 },
              },
            },
            {
              $project: {
                _id: 1,
                name: 1,
                unitCode: 1,
              },
            },
          ],
          as: 'employeeDetails',
        },
      },
      {
        $addFields: {
          hasChildren: {
            $cond: {
              if: { $and: [{ $gt: [{ $size: { $ifNull: [ '$employees', []] } }, 0 ] }, showEmployee === true ] },
              then: true,
              else: { $gt: [{ $size: '$childrenCheck' }, 0 ] },
            },
          },
          category: '$category',
          children: '$employeeDetails',
          totalEmployeeCount: { $ifNull: [ '$totalEmployeeCount', 0 ] },
        },
      },
      {
        $project: {
          childrenCheck: 0,
          level: 0,
          employees: 0,
        },
      },
      {
        $sort: {
          name: 1,
        },
      },
    ];

    ctx.logger.info('子节点聚合管道:', JSON.stringify(pipeline, null, 2));

    const result = await ctx.service.db.aggregate('FlatMillConstructionMaterialized', pipeline);
    ctx.logger.info('子节点查询结果:', { count: result.length });

    // 如果查询结果为空，说明是parentId 是岗位
    if (result.length === 0 && showEmployee) {
      ctx.logger.info('查询结果为空且需要显示员工，尝试查询员工信息...');
      const employeePipeline = [
        {
          $match: {
            _id: params.parentId,
            state: '1',
          },
        },
        {
          $lookup: {
            from: 'employees',
            localField: 'employees',
            foreignField: '_id',
            pipeline: [
              {
                $match: {
                  status: { $ne: 0 },
                },
              },
              {
                $addFields: {
                  hasChildren: {
                    $literal: false,
                  },
                  category: {
                    $literal: 'employees',
                  },
                },
              },
            ],
            as: 'employeeDetails',
          },
        },
        {
          $unwind: '$employeeDetails',
        },
        {
          $replaceRoot: {
            newRoot: '$employeeDetails',
          },
        },
      ];

      ctx.logger.info('员工查询聚合管道:', JSON.stringify(employeePipeline, null, 2));

      const employeeDetails = await ctx.service.db.aggregate('FlatMillConstructionMaterialized', employeePipeline);
      ctx.logger.info('员工查询结果:', { count: employeeDetails.length });
      return employeeDetails;
    }

    ctx.logger.info('查询结果:', { count: result.length });
    return result;

    } catch (error) {
      ctx.logger.error('findMillConstructionLazy 执行失败:', {
        error: error.message,
        stack: error.stack,
        params,
        showEmployee,
      });
      throw error;
    }
  }

  /**
   * @summary 根据岗位id和fullId获取岗位的详细信息
   * @description 从MillConstruction中查询岗位信息，支持两种fullId格式
   * @param {Object} params - 查询参数
   * @param {String} params.stationId - 岗位id
   * @param {String} params.fullId - 完整层级ID
   * @return {Promise<Object>} 岗位详细信息
   */
  async findStationById(params) {
    const { ctx } = this;
    const { fullId } = params;
    // 解析fullId获取层级信息
    const parseFullId = fullId => {
      const parts = fullId.split('_child_workspaces_');
      if (parts.length !== 2) {
        throw new Error('Invalid fullId format');
      }

      const millId = parts[0];
      const remaining = parts[1];

      // 检查是否包含station级别
      if (remaining.includes('_child_stations_')) {
        const stationParts = remaining.split('_child_stations_');
        return {
          millId,
          workspaceId: stationParts[0],
          stationId: stationParts[1],
          hasStation: true,
        };
      }
      return {
        millId,
        workspaceId: remaining,
        hasStation: false,
      };

    };

    try {
      const { millId, workspaceId, stationId: parsedStationId, hasStation } = parseFullId(fullId);

      let pipeline;

      if (hasStation) {
      // Case 1: fullId包含station级别 - 查询到station
        pipeline = [
          {
            $match: { _id: millId },
          },
          {
            $unwind: '$children',
          },
          {
            $match: { 'children._id': workspaceId },
          },
          {
            $unwind: '$children.children',
          },
          {
            $match: { 'children.children._id': parsedStationId },
          },
          {
            $lookup: {
              from: 'employees',
              localField: 'children.children.children.employees',
              foreignField: '_id',
              pipeline: [
                {
                  $match: {
                    status: { $ne: 0 },
                  },
                },
                {
                  $project: { _id: 1, name: 1, unitCode: 1 },
                },
              ],
              as: 'employeeDetails',
            },
          },
          {
            $addFields: {
              'children.children.children': '$employeeDetails',
            },
          },
          {
            $replaceRoot: {
              newRoot: {
                $mergeObjects: [
                  '$children.children',
                  {
                    millName: '$name',
                    workspaceName: '$children.name',
                    children: '$employeeDetails',
                  },
                ],
              },
            },
          },
          {
            $project: {
              employeeDetails: 0,
            },
          },
        ];
      } else {
      // Case 2: fullId只到workspace级别 - workspace就是目标
        pipeline = [
          {
            $match: { _id: millId },
          },
          {
            $unwind: '$children',
          },
          {
            $match: { 'children._id': workspaceId },
          },
          {
            $lookup: {
              from: 'employees',
              localField: 'children.children.employees',
              foreignField: '_id',
              pipeline: [
                {
                  $match: {
                    status: { $ne: 0 },
                  },
                },
                {
                  $project: { _id: 1, name: 1, unitCode: 1 },
                },
              ],
              as: 'employeeDetails',
            },
          },
          {
            $addFields: {
              'children.children': {
                $map: {
                  input: '$children.children',
                  as: 'child',
                  in: {
                    $mergeObjects: [
                      '$$child',
                      {
                        employeeDetails: {
                          $filter: {
                            input: '$employeeDetails',
                            cond: { $eq: [ '$$this._id', '$$child.employees' ] },
                          },
                        },
                      },
                    ],
                  },
                },
              },
            },
          },
          {
            $replaceRoot: {
              newRoot: {
                $mergeObjects: [
                  '$children',
                  {
                    millName: '$name',
                    children: '$children.children',
                  },
                ],
              },
            },
          },
        ];
      }

      const result = await ctx.service.db.aggregate('MillConstruction', pipeline, { authCheck: false });
      return result[0] || null;

    } catch (error) {
      ctx.logger.error('findStationById error:', error);
      throw error;
    }
  }

}

module.exports = MillService;
