const path = require('path');
const sendToWormhole = require('stream-wormhole');
const shortid = require('shortid');

const MillController = {
  // SSE 进度推送接口
  async addMillProgress(ctx) {
    // 直接使用原生 Node.js 响应，绕过 Egg.js
    const res = ctx.res;

    try {
      console.log('🚀 SSE 连接已建立，开始处理数据...');
      console.log('📋 请求信息:', {
        method: ctx.method,
        url: ctx.url,
        accept: ctx.get('Accept'),
        contentType: ctx.get('Content-Type'),
      });

      // 设置 SSE 响应头
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
      });

      // 阻止 Egg.js 自动处理响应
      ctx.respond = false;

      // 禁用请求超时
      ctx.req.setTimeout(0);
      res.setTimeout(0);

      // 获取请求参数
      const params = ctx.request.body;

      // 验证参数
      if (!params || !params.mills || !Array.isArray(params.mills)) {
        ctx.res.write(`data: ${JSON.stringify({
          type: 'error',
          message: '参数错误：缺少 mills 数组',
        })}\n\n`);
        ctx.res.end();
        return;
      }

      // 发送连接成功消息
      console.log('📋 发送连接消息...');
      res.write(`data: ${JSON.stringify({
        type: 'connected',
        message: '连接成功，开始处理数据...',
        total: params.mills.length,
      })}\n\n`);
      console.log('✅ 连接消息发送成功');

      // 设置心跳保持连接
      const heartbeat = setInterval(() => {
        if (!res.finished && !res.destroyed) {
          try {
            res.write(`data: ${JSON.stringify({
              type: 'heartbeat',
              timestamp: Date.now(),
            })}\n\n`);
          } catch (error) {
            console.error('心跳发送失败:', error);
            clearInterval(heartbeat);
          }
        } else {
          clearInterval(heartbeat);
        }
      }, 5000); // 每5秒发送一次心跳

      try {
        // 调用 service 处理数据（带 SSE 支持）
        // 在参数中明确标识这是 SSE 请求
        params._isSSE = true;
        await ctx.service.mill.addMill(params);
      } finally {
        // 清理心跳
        clearInterval(heartbeat);
      }

      // 如果 service 没有结束连接，这里结束
      if (!res.finished && !res.destroyed) {
        console.log('🔚 控制器结束 SSE 连接');
        res.end();
      }

    } catch (error) {
      console.error('❌ SSE 处理出错:', error);

      // 发送错误消息
      if (!res.finished && !res.destroyed) {
        res.write(`data: ${JSON.stringify({
          type: 'error',
          message: error.message || '处理过程中发生错误',
        })}\n\n`);
        res.end();
      } else {
        console.error('⚠️  无法发送错误消息，响应已结束');
      }
    }
  },

  // // 普通的 addMill 接口（不使用 SSE）
  // async addMillComo() {
  //   const { ctx } = this;

  //   try {
  //     const params = ctx.request.body;

  //     // 验证参数
  //     if (!params || !params.mills || !Array.isArray(params.mills)) {
  //       ctx.body = {
  //         code: 400,
  //         message: '参数错误：缺少 mills 数组',
  //       };
  //       return;
  //     }

  //     // 调用 service 处理数据（不使用 SSE）
  //     const result = await ctx.service.mill.addMill(params);

  //     ctx.body = {
  //       code: 200,
  //       message: '处理成功',
  //       data: result,
  //     };

  //   } catch (error) {
  //     console.error('addMill 处理出错:', error);

  //     ctx.body = {
  //       code: 500,
  //       message: error.message || '处理过程中发生错误',
  //     };
  //   }
  // },
  async getMillStatistics(ctx) {
    try {
      const data = await ctx.service.mill.getMillStatistics(ctx.request.body);
      ctx.helper.renderSuccess(ctx, {
        status: 200,
        data,
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },
  // 添加厂房
  async addMill(ctx) {
    const params = ctx.request.body;
    const res = await ctx.service.mill.addMill(params);
    ctx.body = res === 500 ? { code: 500, message: '厂房/车间已存在' } : { code: 200, message: '添加成功' };
  },

  // 添加车间
  async addWorkSpace(ctx) {
    const params = ctx.request.body;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    params.EnterpriseID = EnterpriseID;
    const res = await ctx.service.mill.addWorkSpace(params);
    ctx.body = res === 500 ? { code: 500, message: '车间已存在' } : { code: 200, message: '添加成功' };
  },

  // 添加岗位
  async addStation(ctx) {
    const params = ctx.request.body;
    const res = await ctx.service.mill.addStation(params);
    ctx.body = res === 500 ? { code: 500, message: '岗位已存在' } : { code: 200, message: '添加成功' };
  },

  // 查询厂房架构
  async findMillConstruction(ctx, app) {
    const branch = app.config.branch;
    const params = ctx.request.body;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    if (!params.EnterpriseID) {
      params.EnterpriseID = EnterpriseID;
    }
    const res = await ctx.service.mill.findMillConstruction(params);
    let excelTemp = '';
    // let excelTemp = `${app.config.static.prefix}${app.config.data_template_path}/工作场所模板.xlsx`;
    const initWorkshopStation = await ctx.service.mill.getInitStationSwitch();
    if (branch === 'wh') {
      excelTemp = `${app.config.static.prefix}${app.config.data_template_path}/工作场所模板_wh.xlsx`;
      initWorkshopStation && (excelTemp = `${app.config.static.prefix}${app.config.data_template_path}/工作场所模板-初始化车间岗位_wh.xlsx`);
    } else {
      excelTemp = `${app.config.static.prefix}${app.config.data_template_path}/工作场所模板.xlsx`;
      initWorkshopStation && (excelTemp = `${app.config.static.prefix}${app.config.data_template_path}/工作场所模板-初始化车间岗位.xlsx`);
    }
    // initWorkshopStation && (excelTemp = `${app.config.static.prefix}${app.config.data_template_path}/工作场所模板-初始化车间岗位.xlsx`);
    // console.log(res, '厂房结构');
    ctx.body = res === 500 ? { code: 500, message: '服务器出错' } : { code: 200, status: 200, data: res, excelTemp };
  },
  async getTemplate(ctx, app) {
    const branch = app.config.branch;
    let excelTemp = '';
    const initWorkshopStation = await ctx.service.mill.getInitStationSwitch();
    if (branch === 'wh') {
      excelTemp = `${app.config.static.prefix}${app.config.data_template_path}/工作场所模板_wh.xlsx`;
      initWorkshopStation && (excelTemp = `${app.config.static.prefix}${app.config.data_template_path}/工作场所模板-初始化车间岗位_wh.xlsx`);
    } else {
      excelTemp = `${app.config.static.prefix}${app.config.data_template_path}/工作场所模板.xlsx`;
      initWorkshopStation && (excelTemp = `${app.config.static.prefix}${app.config.data_template_path}/工作场所模板-初始化车间岗位.xlsx`);
    }
    ctx.body = { code: 200, data: excelTemp };
  },
  async findMillByEnterprise(ctx) {
    const params = ctx.request.query;
    const res = await ctx.service.mill.findMillByEnterprise(params);
    ctx.body = res === 500 ? { code: 500, message: '服务器出错' } : { code: 200, status: 200, data: res };
  },

  // 查询防护用具
  async protectiveEquipment(ctx) {
    const params = ctx.request.body;
    // console.log(params, 'controller查询防护用具');
    const res = await ctx.service.mill.protectiveEquipment(params);
    ctx.body = res === 500 ? { code: 500, message: '服务器出错' } : { code: 200, status: 200, data: res };
  },

  // 查询员工
  async findEmployees(ctx) {
    const params = ctx.request.body;
    const res = await ctx.service.mill.findEmployees(params);
    ctx.body = res === 500 ? { code: 500, message: '服务器出错' } : { code: 200, data: res };
  },

  // 添加员工
  async addEmployee(ctx) {
    const params = ctx.request.body;
    const res = await ctx.service.mill.addEmployee(params);
    ctx.body = res === 500 ? { code: 500, message: '员工已存在' } : { code: 200, message: '添加成功' };
  },

  // 修改厂房名称
  async editMill(ctx) {
    try {
      const params = ctx.request.body;
      const result = await ctx.service.mill.editMill(params);
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '修改成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '服务器出错',
      });
    }
  },

  // 修改车间信息
  async editWorkspace(ctx) {
    try {
      const params = ctx.request.body;
      const result = await ctx.service.mill.editWorkspace(params);
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '修改成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '服务器出错',
      });
    }
  },

  // 修改岗位信息
  async editStation(ctx) {
    try {
      const params = ctx.request.body;
      let EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
      EnterpriseID = params.enterpriseId || EnterpriseID;

      const result = await ctx.service.mill.editStation(params);

      // 发送通知记录
      await ctx.service.zyjcRecord.noticeRecord3({
        EnterpriseID,
        collection: 'mill',
        recordType: '-4',
      });
      if (result.status === 500) {

        return ctx.helper.renderFail(ctx, {
          message: result.message,
          status: result.status,
        });
      }

      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '修改成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message || '服务器出错',
      });
    }
  },

  // 删除岗位
  async deleteStation(ctx) {
    const params = ctx.request.body;
    const res = await ctx.service.mill.deleteStation(params);
    // wzq +
    let EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    EnterpriseID = params.enterpriseId || EnterpriseID;
    await ctx.service.warnNotice.delWarnNotice(EnterpriseID, params);
    // wzq +
    ctx.body = res === 500 ? { code: 500, message: '服务器出错' } : { code: 200, message: '删除成功' };
  },
  // 删除车间
  async deleteWorkspace(ctx) {
    const params = ctx.request.body;
    const res = await ctx.service.mill.deleteWorkspace(params);
    // wzq +
    let EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    EnterpriseID = params.enterpriseId || EnterpriseID;
    await ctx.service.warnNotice.delWarnNotice(EnterpriseID, params);
    // wzq +
    ctx.body = res === 500 ? { code: 500, message: '服务器出错' } : { code: 200, message: '删除成功' };
  },

  // 删除厂房
  async deleteMill(ctx) {
    const params = ctx.request.body;
    const res = await ctx.service.mill.deleteMill(params);
    // wzq +
    let EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    EnterpriseID = params.enterpriseId || EnterpriseID;
    await ctx.service.warnNotice.delWarnNotice(EnterpriseID, params);
    // wzq +
    ctx.body = res === 500 ? { code: 500, message: '服务器出错' } : { code: 200, message: '删除成功' };
  },
  // 删除员工
  async deleteEmployee(ctx) {
    const params = ctx.request.body;
    const res = await ctx.service.mill.deleteEmployee(params);
    ctx.body = res === 500 ? { code: 500, message: '服务器出错' } : { code: 200, message: '删除成功' };
  },

  // 拖拽更新员工
  async dropUpdateEmployee(ctx) {
    const params = ctx.request.body;
    const res = await ctx.service.mill.dropUpdateEmployee(params);
    ctx.body = res === 500 ? { code: 500, message: '服务器出错' } : { code: 200, message: '更新成功' };
  },

  // 拖拽更新岗位
  async dropUpdateStation(ctx) {
    const params = ctx.request.body;
    const res = await ctx.service.mill.dropUpdateStation(params);
    ctx.body = res === 500 ? { code: 500, message: '服务器出错' } : { code: 200, message: '更新成功' };
  },

  // 拖拽更新车间
  async dropUpdateWorkspace(ctx) {
    const params = ctx.request.body;
    const res = await ctx.service.mill.dropUpdateWorkspace(params);
    ctx.body = res === 500 ? { code: 500, message: '服务器出错' } : { code: 200, message: '更新成功' };
  },

  // 拖拽更新厂房
  async dropUpdateMill(ctx) {
    const params = ctx.request.body;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    params.EnterpriseID = EnterpriseID;
    const res = await ctx.service.mill.dropUpdateMill(params);
    ctx.body = res === 500 ? { code: 500, message: '服务器出错' } : { code: 200, message: '更新成功' };
  },

  // 复制车间
  async copyWorkspace(ctx) {
    const params = ctx.request.body;
    const res = await ctx.service.mill.copyWorkspace(params);
    ctx.body = res === 500 ? { code: 500, message: '服务器出错' } : { code: 200, message: '复制成功' };
  },

  // 复制岗位
  async copyStation(ctx) {
    const params = ctx.request.body;
    const res = await ctx.service.mill.copyStation(params);
    ctx.body = res === 500 ? { code: 500, message: '服务器出错' } : { code: 200, message: '复制成功' };
  },

  async copyEmployee(ctx) {
    const params = ctx.request.body;
    const res = await ctx.service.mill.copyEmployee(params);
    if (res.errMsg) {
      ctx.body = { code: 201, message: res.errMsg };
    } else if (res === 500) {
      ctx.body = { code: 500, message: '服务器出错' };
    } else {
      ctx.body = { code: 200, message: '复制成功' };
    }
  },

  // 导入危害因素数据
  async importHarmFactorData(ctx) {
    const params = ctx.request.body;
    const res = await ctx.service.mill.importHarmFactorData(params.importData);
    ctx.body = res === 500 ? { code: 500, message: '服务器出错' } : { code: 200, message: '导入成功' };
  },

  // 查询所有危害因素
  async findAllHarmFactors(ctx) {
    const res = await ctx.service.mill.findAllHarmFactors();
    ctx.body = res === 500 ? { code: 500, message: '服务器出错' } : { code: 200, data: res };
  },

  // 批量人员转岗
  async moveEmployees(ctx) {
    const params = ctx.request.body;
    console.log(params, '前台的参数');
    const res = await ctx.service.mill.moveEmployees(params);
    if (res.errMsg) {
      ctx.body = { code: 201, message: res.errMsg };
    } else if (res === 500) {
      ctx.body = { code: 500, message: '服务器出错' };
    } else {
      ctx.body = { code: 200, message: '转岗成功' };
    }
  },

  // 根据id查询员工信息以及危害因素等接害情况
  async findEmployeeById(ctx, app) {
    const params = ctx.request.body;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    params.EnterpriseID = params.enterpriseId || EnterpriseID;
    delete params.enterpriseId;
    try {
      const res = await ctx.service.mill.findEmployeeById(params);
      const filePath = await ctx.helper.concatenatePath({
        path: `${app.config.upload_http_path}/${EnterpriseID}`,
      });
      ctx.body = { code: 200, ...res, filePath };
    } catch (error) {
      console.log(error);
      ctx.body = { code: 500, message: '服务器出错' };
    }

  },

  // 查询岗位的危害检测结果
  async getCheckResult(ctx) {

    try {
      const params = ctx.request.body;
      const harmArr = [];
      params[0].harmFactorsAndSort.forEach(item => {
        if (!item) return;
        harmArr.push(item[1]);
      });
      const materialInfoList = await ctx.service.mill.getMaterialInfoList(harmArr);
      const res = await ctx.service.mill.getCheckResult(params);
      const data = {
        res,
        materialInfoList,
      };
      ctx.body = { code: 200, data };
    } catch (error) {
      console.log(error);
      ctx.body = { code: 500, message: '服务器出错' };
    }
  },

  // 查找体检结果
  async findHealthheckResult(ctx) {
    try {
      const params = ctx.request.body;
      const res = await ctx.service.mill.findHealthheckResult(params);
      console.log(res);
      ctx.body = { code: 200, data: res };
    } catch (error) {
      console.log(error);
      ctx.body = { code: 500, message: '服务器出错' };
    }
  },

  // 工作场所关联部门
  async workshopLinkDeparts(ctx) {
    const { data, stationId, workspaceId, millId } = ctx.request.body;
    console.log(31231, data, stationId, workspaceId, millId);

    const res = await ctx.service.mill.workshopLinkDeparts({ data, stationId, workspaceId, millId });

    ctx.helper.renderSuccess(ctx, {
      status: 200,
      data: res,
    });
  },

  // 同步工作场所关联的部门人员
  async updateWrokPlaceLinkDeparts(ctx, app) {

    const branch = app.config.branch;
    if (branch === 'by') {
      await ctx.service.mill.updateOldLinkDepartsToNew();
    }

    const res = await ctx.service.mill.updateWrokPlaceLinkDeparts();
    ctx.helper.renderSuccess(ctx, {
      status: 200,
      data: res,
    });
  },

  // 北元获取待审核人员名单
  async getByReviewList(ctx) {
    const query = ctx.request.query;
    try {
      const res = await ctx.service.mill.getByReviewList(query);
      for (let i = 0; i < res.data.length; i++) {
        const item = res.data[i];
        if (item.files && item.files.originName) {
          item.files.url = await ctx.helper.concatenatePath({
            path: `${ctx.app.config.upload_http_path}/${item.nowEnterpriseID}/${item.files.staticName}`,
          });
        }
        if (item.filesInfo && item.filesInfo.length) {
          for (let i = 0; i < item.filesInfo.length; i++) {
            const ele = item.filesInfo[i];
            if (ele.originName) {
              ele.url = await ctx.helper.concatenatePath({
                path: `${ctx.app.config.upload_http_path}/${item.nowEnterpriseID}/${ele.staticName}`,
              });
            }
          }
        }
      }
      ctx.helper.renderSuccess(ctx, {
        status: 200,
        data: res,
      });
    } catch (error) {
      ctx.helper.renderCustom(ctx, {
        message: error,
        status: 500,
      });
    }
  },

  async reviewByEmployee(ctx) {
    const params = ctx.request.body;
    try {
      const res = await ctx.service.mill.reviewByEmployee(params);
      ctx.helper.renderSuccess(ctx, {
        status: 200,
        data: res,
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderCustom(ctx, {
        message: error,
        status: 500,
      });
    }
  },

  async uploadByEmployeeFile(ctx) {
    try {
      const stream = await ctx.getFileStream();

      const { selectedId, EnterpriseID, employeeId } = stream.fields;
      console.log(stream.fields, selectedId, EnterpriseID, 123);
      const name = stream.filename;
      const fileExtension = name.split('.').pop();
      if (
        fileExtension !== 'docx' &&
        fileExtension !== 'pdf' &&
        fileExtension !== 'jpg' &&
        fileExtension !== 'png' &&
        fileExtension !== 'jpeg'
      ) {
        ctx.helper.renderCustom(ctx, {
          message: '文件格式不正确',
          data: '文件格式不正确',
          status: 500,
        });
        return;
      }
      const timestamp = new Date().getTime();
      const randomCode = Math.floor(Math.random() * 1000000)
        .toString()
        .padStart(6, '0');
      const newFilePath = `${timestamp}${randomCode}.${fileExtension}`;
      const filePath = path.join(
        ctx.app.config.upload_path,
        EnterpriseID,
        `/${newFilePath}`
      );
      await ctx.helper.pipe({
        readableStream: stream,
        target: filePath,
      });
      const files = {
        originName: name,
        staticName: newFilePath,
      };
      const res = await ctx.service.mill.uploadByEmployeeFile({
        _id: selectedId,
        files,
        employeeId,
        fileExtension,
      });
      ctx.helper.renderSuccess(ctx, {
        status: 200,
        message: '上传成功',
        data: res,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  async getByEmployeeNum(ctx) {
    const EnterpriseID = ctx.session.adminUserInfo.EnterpriseID;
    try {
      const res = await ctx.service.mill.getByEmployeeNum(EnterpriseID);
      ctx.helper.renderSuccess(ctx, {
        status: 200,
        data: res,
      });
    } catch (error) {
      ctx.helper.renderCustom(ctx, {
        message: error,
        status: 500,
      });
    }
  },

  async updateChangeStationInfo(ctx) {
    // 多文件上传
    const { EnterpriseID } = ctx.session.adminUserInfo;
    const parts = ctx.multipart({ autoFields: true });
    let part;
    const fileInfo = [];
    while ((part = await parts()) != null) {
      if (!part.filename) {
        return;
      }
      // 获取其他formData参数
      // 处理文件流
      const timestamp = new Date().getTime();
      const randomCode = Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
      // 拿到文件后缀
      const extname = path.extname(part.filename);
      // 拼接成新的文件名
      const filename = timestamp + randomCode + extname;
      // 处理得到静态资源路径地址
      const filePath = path.join(ctx.app.config.upload_path, EnterpriseID);
      // if (!fs.existsSync(filePath)) {
      //   fs.mkdirSync(filePath);
      // }
      const target = path.resolve(filePath, filename);

      // 读流
      // const writeStream = fs.createWriteStream(target);
      try {
        // 写流
        // await awaitWriteStream(part.pipe(writeStream));
        await ctx.helper.pipe({
          readableStream: part,
          target,
        });
        fileInfo.push({
          _id: shortid.generate(),
          originName: part.filename,
          staticName: filename,
        });
      } catch (error) {
        console.log(error);
        await sendToWormhole(part);
        // writeStream.destroy();
        throw error;
      }
    }
    const fieldData = await parts.fields;
    const data = JSON.parse(fieldData[1][1]);
    const deleteAttachment = JSON.parse(fieldData[0][1]);
    if (deleteAttachment.length > 0) {
      deleteAttachment.forEach(item => {
        const fileName = item.url.split('?')[0].split('/').pop();
        const filePath = path.join(
          ctx.app.config.upload_path,
          EnterpriseID,
          fileName
        );
        // if (fs.existsSync(filePath)) {
        //   fs.unlinkSync(filePath);
        // }
        data.filesInfo = data.filesInfo.filter(subItem => subItem.staticName !== fileName);

        ctx.helper.deleteObject(filePath);
        // 删除attachment中的数据
      });
    }
    for (let i = 0; i < fileInfo.length; i++) {
      for (let j = 0; j < data.filesInfo.length; j++) {
        if (data.filesInfo[j].originName === fileInfo[i].originName) {
          data.filesInfo[j].staticName = fileInfo[i].staticName;
        }
      }
    }
    // 根据data._id判断是新建还是更新
    if (data._id) {
      // 更新
      try {
        const res = await ctx.service.mill.updateChangeStationInfo(data);
        ctx.helper.renderSuccess(ctx, {
          status: 200,
          message: '更新成功',
          data: res,
        });
      } catch (error) {
        console.log(error);
        ctx.helper.renderCustom(ctx, {
          message: error,
          status: 500,
        });
      }
    }
  },

  // 根据装置查询危害因素列表
  async getListByDevice(ctx) {
    try {
      const { query } = ctx;

      let defaultEnterpriseID = '';
      if (ctx.session.adminUserInfo && ctx.session.adminUserInfo.EnterpriseID) {
        defaultEnterpriseID = ctx.session.adminUserInfo.EnterpriseID;
      }

      // 构建查询参数，包含筛选条件
      const params = {
        EnterpriseID: query.EnterpriseID || defaultEnterpriseID,
        department: query.department || '',
        device: query.device || '',
        process: query.process || '',
        workType: query.workType || '',
        currentPage: parseInt(query.currentPage) || 1,
        pageSize: parseInt(query.pageSize) || 10,
      };

      // 调用服务层方法
      const res = await ctx.service.mill.getListByDevice(params);

      // 返回成功响应，同时返回默认企业ID供前端使用
      ctx.helper.renderSuccess(ctx, {
        status: 200,
        data: {
          ...res,
          defaultEnterpriseID, // 返回默认企业ID给前端
        },
      });
    } catch (error) {
      console.error('getListByDevice controller error:', error);
      ctx.helper.renderCustom(ctx, {
        message: '获取数据失败',
        status: 500,
      });
    }
  },

  /**
   * @summary 懒加载获取工厂结构数据
   * @description 基于扁平化视图实现层级懒加载查询，提高页面加载性能
   * @param {Context} ctx - Egg.js上下文对象
   */
  async findMillConstructionLazy(ctx) {
    try {
      const params = ctx.request.query || {};
      const showEmployee = params.showEmployee === 'true' || false;

      // 添加详细的调试日志
      ctx.logger.info('findMillConstructionLazy 控制器开始执行:', {
        url: ctx.request.url,
        method: ctx.request.method,
        params,
        showEmployee,
        userAgent: ctx.request.header['user-agent'],
        ip: ctx.request.ip,
      });

      // 调用服务层方法
      const result = await ctx.service.mill.findMillConstructionLazy(params, showEmployee);

      ctx.logger.info('findMillConstructionLazy 控制器执行成功:', {
        resultCount: result ? result.length : 0,
        resultData: JSON.stringify(result, null, 2),
      });

      // 使用标准响应格式
      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '查询成功',
      });

      ctx.logger.info('findMillConstructionLazy 响应已发送:', {
        status: ctx.status,
        bodyStatus: ctx.body ? ctx.body.status : 'undefined',
        bodyMessage: ctx.body ? ctx.body.message : 'undefined',
        bodyDataCount: ctx.body && ctx.body.data ? ctx.body.data.length : 'undefined',
      });

    } catch (error) {
      // 记录详细的错误日志
      ctx.logger.error('懒加载查询工厂结构失败:', {
        error: error.message,
        stack: error.stack,
        url: ctx.request.url,
        params: ctx.request.query,
        userInfo: ctx.session && ctx.session.adminUserInfo && ctx.session.adminUserInfo._id,
      });

      // 返回错误响应
      ctx.helper.renderFail(ctx, {
        message: error.message || '查询失败，请稍后重试',
      });
    }
  },

  async findStationById(ctx) {
    const params = ctx.request.query;
    const res = await ctx.service.mill.findStationById(params);
    ctx.body = res === 500 ? { code: 500, message: '服务器出错' } : { code: 200, status: 200, data: res };
  },
};
module.exports = MillController;
